/**
 * 商品信息接口
 */
interface Goods {
  /**
   * 商品ID
   */
  goodsMasterId: string;

  /**
   * 子规格商品ID (非必须)
   */
  specificationItemId?: string;

  /**
   * 商品名称
   */
  goodsTitle: string;

  /**
   * 商品图片URL
   */
  goodsImg: string;

  /**
   * 商品现价
   */
  goodsPresentPrice: number;

  /**
   * 商品库存
   */
  stock: number;

  /**
   * 可砍至X元的价格
   */
  bargainPrice: number;

  /**
   * 用户是否参与了砍价活动
   */
  participatedInActivities: boolean;

  /**
   * 砍价进度 (非必须)
   */
  bargainProgress?: number;
}

export interface BargainInfo {
   /**
   * 活动ID
   */
   id: string;

   /**
    * PC端营销图URL
    */
   pcMarketingImageUrl: string;

   /**
    * 移动端营销图URL
    */
   wapMarketingImageUrl: string;

   /**
    * 活动名称
    */
   activityName: string;

   /**
    * 活动开始时间
    */
   activityBeginTime: string;

   /**
    * 活动结束时间
    */
   activityEndTime: string;

   /**
    * 商品列表
    */
   goodsList: Goods[];
}

/**
 * 砍价活动校验
 */
export interface VerifyActivity {
  /**
   * 活动ID
   */
  activityId: string;

  /**
   * 商品ID
   */
  goodsMasterId: string;

  /**
   * 子规格商品ID (非必须)
   */
  specificationItemId?: string;
}

/**
 * 查询商品参加的营销活动信息参数
 */
export interface GoodsActivity {
  /**
   * 商品ID
   */
  goodsMasterId: string;

  /**
   * 子规格商品ID (非必须)
   */
  goodsSpecificationItemId?: string;

  /**
   * 推广员ID (非必须)
   */
  extensionUserId?: string;

  /**
   * 是否是砍价活动 (非必须)
   * 1 表示是砍价活动，0 或不提供表示不是砍价活动
   */
  isBargain?: number;
}

/**
 * 商品营销活动接口
 */
export interface GoodsMarketingActivity {
  /**
   * 是否参加了秒杀拼团及分销活动
   * 0 否，1 是
   */
  isMarketing?: number;

  /**
   * 是否参加了买赠活动
   * 0 否，1 是
   */
  isBuyGive?: number;

  /**
   * 活动标题
   */
  activityTitle?: string;

  /**
   * 营销活动类型
   * 1 - 拼团
   * 2 - 秒杀
   * 3 - 分销
   * 4 - 加价购
   * 12 - 砍价
   */
  activityType?: number;

  /**
   * 活动ID
   */
  activityId?: string;

  /**
   * 活动状态
   * 1 - 未开始
   * 2 - 已开始
   */
  activityStatus?: number;

  /**
   * 倒计时（单位毫秒）
   */
  countDown?: number;

  /**
   * 活动开始时间
   */
  activityStartTime?: string;

  /**
   * 活动结束时间
   */
  activityEndTime?: string;

  /**
   * 活动价格
   */
  activityPrice?: string;

  /**
   * 商品ID
   */
  goodsMasterId?: string;

  /**
   * 子规格ID
   */
  goodsSpecificationItemId?: string;

  /**
   * 商品是否参加过分销活动
   * 1 是
   * 0 否
   */
  isDistributionGoods?: number;

  /**
   * 拼团倒计时
   * 0 关闭，1 开启
   */
  teamworkCountdown?: number;

  /**
   * 分销活动信息
   */
  distributionProperty?: object;

  /**
   * 拼团活动信息
   */
  groupProperty?: object;

  /**
   * 砍价活动信息
   */
  bargainProperty?: object;
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  /**
   * 用户名
   * 必须
   */
  userName: string;

  /**
   * 头像
   * 必须
   */
  photo: string;

  /**
   * 邀请时间
   * 必须，ISO 8601 格式
   */
  createdAt: string;

  /**
   * 砍掉的金额
   * 必须，数字类型，表示活动中用户砍掉的金额
   */
  subtractedPrice: number;
}
interface BargainUserInfo {
  /**
   * 用户名
   * 必须，表示用户的姓名或昵称
   */
  userName: string;

  /**
   * 头像
   * 必须，表示用户的头像URL
   */
  photo: string;

  /**
   * 邀请时间
   * 必须，表示用户参与活动的时间
   */
  createdAt: string;

  /**
   * 砍掉的金额
   * 必须，表示用户已经砍掉的金额
   */
  subtractedPrice: number;
}
/**
 * 砍价记录
 */
export interface RecordList {
  /**
   * 总条数
   * 必须，表示数据的总条数
   */
  count: number;

  /**
   * 数据列表
   * 必须，包含具体数据的对象数组
   */
  list: BargainUserInfo[];
}
