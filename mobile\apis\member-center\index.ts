export enum MemberCenterApi {
  // 折扣权益商品列表（兼容查看更多）
  rightsGoodsList = '/kukemarketing/wap/memberRightsGoods/discountGoodsList',
  // 免费权益列表（兼容查看更多）
  freeGoodsList = '/kukemarketing/wap/memberRightsGoods/freeGoodsList',
  // 权益商品列表筛选专业分类
  rightsCateIds = '/kukemarketing/wap/memberRightsGoods/rightsCateIds',
  // 权益商品列表筛选标签
  rightsLabelIds = '/kukemarketing/wap/memberRightsGoods/rightsLabelIds',
  // 会员中心专业分类筛选
  memberLabel = '/kukemarketing/wap/kmMember/memberLabel',
  // 会员中心专业分类和二级标签筛选
  memberSecondLabel = '/kukemarketing/wap/kmMember/memberSecondLabel',
  // 赠送优惠券列表（兼容查看更多）
  giftCouponList = '/kukemarketing/wap/memberRightsGoods/giftCouponList',
  // 会员中心详情-是否开启会员中心
  openMembership = '/kukemarketing/wap/kmMember/memberDetails',
  // 会员详情
  memberDetail = '/kukemarketing/wap/kmMember/memberGoodsDetailsProt',
  // 会员订单
  addMemberOrderProt = '/kukeonlineorder/wap/order/addMemberOrderProt',
  // 计算使用会员后的商品价格
  memberDiscountPrice = '/kukemarketing/wap/kmMember/getUseMemberPriceProt',
  // 会员入口-用户已开通会员
  openMemberList = '/kukemarketing/wap/memberUser/openMemberListProt',
  // 会员中心-会员商品列表
  memberGoodsList = '/kukemarketing/wap/memberUser/memberGoodsList',
  // 会员中心-查看会员权益
  memberRights = '/kukemarketing/wap/kmMember/getBuyMemberGoodsRightsProt',
  // 会员中心-用户会员信息
  memberInfo = '/kukemarketing/wap/memberUser/memberInfoProt',
}

/**
 * @description 折扣权益商品列表（兼容查看更多）
 *
 * @param body
 * @returns
 */
export async function getRightsGoodsListProd (body: any) {
  return useHttp<any>(MemberCenterApi.rightsGoodsList, {
    method: 'post',
    body
  })
}

/**
 * @description 免费权益列表（兼容查看更多）
 *
 * @param body
 * @returns
 */
export async function getFreeGoodsListProd (body: any) {
  return useHttp<any>(MemberCenterApi.freeGoodsList, {
    method: 'post',
    body
  })
}

/**
 * @description 权益商品列表筛选专业分类
 *
 * @param body
 * @returns
 */
export async function getRightsCateIdsListProd (body: any) {
  return useHttp<any>(MemberCenterApi.rightsCateIds, {
    method: 'post',
    body,
    transform: res => res.data
  })
}

/**
 * @description 权益商品列表筛选标签
 *
 * @param body
 * @returns
 */
export async function getRightsLabelIdsListProd (body: any) {
  return useHttp<any>(MemberCenterApi.rightsLabelIds, {
    method: 'post',
    body,
    transform: res => res.data
  })
}

/**
 * @description 会员中心专业分类筛选
 * @description 优惠券免费得列表
 *
 * @param body
 * @returns
 */
export async function getGiftCouponList (body: any) {
  return useHttp<any>(MemberCenterApi.giftCouponList, {
    method: 'post',
    body
  })
}

/**
 * @description 会员详情
 *
 * @param body
 * @returns
 */
export async function getMemberDetailsProt (body: any) {
  return useHttp<any>(MemberCenterApi.memberDetail, {
    method: 'post',
    body
  })
}

/**
 * @description 会员订单
 *
 * @param body
 * @returns
 */
export async function addMemberOrderProt (body: any) {
  return useHttp<any>(MemberCenterApi.addMemberOrderProt, {
    method: 'post',
    body
  })
}

/**
 * @description 计算使用会员后的商品价格
 *
 * @param body
 * @returns
 */
export async function getMemberDiscountPriceProd (body: any) {
  return useHttp<any>(MemberCenterApi.memberDiscountPrice, {
    method: 'post',
    body,
    transform: res => res.data
  })
}

/**
 * @description 会员中心详情-是否开启会员中心
 *
 * @param body
 * @returns
 */
export async function getOpenMembership (body: any) {
  return useHttp<any>(MemberCenterApi.openMembership, {
    method: 'post',
    body
  })
}

/**
 * @description 会员中心专业分类筛选
 * @description 优惠券免费得列表
 *
 * @param body
 * @returns
 */
export async function getMemberLabelProd (body: any) {
  return useHttp<any>(MemberCenterApi.memberLabel, {
    method: 'post',
    body
  })
}

/**
 * @description 会员中心专业分类和二级标签筛选
 *
 * @param body
 * @returns
 */
export async function getMemberSecondLabelProd (body: any) {
  return useHttp<any>(MemberCenterApi.memberSecondLabel, {
    method: 'post',
    body
  })
}

/**
 * @description 会员入口-用户已开通会员
 *
 * @param body
 * @returns
 */
export async function getOpenMemberListProt (body: any) {
  return useHttp<any>(MemberCenterApi.openMemberList, {
    method: 'post',
    body
  })
}

/**
 * @description 会员中心-会员商品列表
 *
 * @param body
 * @returns
 */
export async function getMemberGoodsList (body: any) {
  return useHttp<any>(MemberCenterApi.memberGoodsList, {
    method: 'post',
    body
  })
}

/**
 * @description 会员中心-查询购买过会员商品后权益是否下发完成
 *
 * @param body
 * @returns
 */
export async function getMemberRightsIssueProd (body: any) {
  return useHttp<any>(MemberCenterApi.memberRights, {
    method: 'post',
    body,
    transform: res => res.data
  })
}

/**
 * @description 会员中心-用户会员信息
 *
 * @param body
 * @returns
 */
export async function getMemberInfoProt (body: any) {
  return useHttp<any>(MemberCenterApi.memberInfo, {
    method: 'post',
    body
  })
}
