import type { AgreementDetailFetchParams, FetchAgreementDetailResponseData, FetchAgreementListResponseData } from './types'
import { agreementSceneTypeEnum } from '~/constants'

enum AgreementApi {
  getAgreementListBySceneType = '/kukebasesystem/index/agreement/queryList', // 根据场景值获取协议列表
  agreementDetail = '/kukebasesystem/index/agreement/queryDetail', // 协议详情
}

/**
 *根据场景类型获取用户协议
 * @param sceneType 场景类型
 * @returns
 */
export async function getAgreementListBySceneType (sceneType: agreementSceneTypeEnum) {
  return useHttp<FetchAgreementListResponseData>(AgreementApi.getAgreementListBySceneType, {
    method: 'post',
    body: { sceneType },
    transform: (res: any) => res.data,
  })
}

/**
 * 请求协议详情
 * @param body
 * @returns
 */
export async function getAgreementDetail (body: AgreementDetailFetchParams) {
  return useHttp<FetchAgreementDetailResponseData>(AgreementApi.agreementDetail, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
