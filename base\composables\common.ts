/* 更新用户行为 */
export const updateBehaviorRecords = (uuid: string, source: number | string, options: any = {}) => {
  const { appId, type } = useAppConfig()
  const kkInternalSystem = useCookie<string>(INNERNAL_SYSTEM)
  const body = {
    behaviorClassificationUuid: uuid,
    behaviorSource: source,
    ...options,
    ...(kkInternalSystem.value && Number(kkInternalSystem.value) === 37 ? { extension1: '37' } : {})// scrm所有的行为记录都传系统标识，不能转化为线索
  }
  if (type === 2) {
    body.productId = 1000
  } else {
    body.productId = appId
  }
  useHttp<any>(ApisCommon.addBehaviorRecor, {
    body
  })
}

/**
 * 购物车提交订单行为
 */
export const fetchAddOrdersBehaviorRecord = async (uuid: string, source: number | string, options: any = {}) => {
  const { appId, type } = useAppConfig()
  const body = {
    behaviorClassificationUuid: uuid,
    behaviorSource: source,
    ...options,
  }
  if (type === 2) {
    body.productId = 1000
  } else {
    body.productId = appId
  }

  return useHttp<any>(ApisCommon.fetchAddOrdersBehaviorRecord, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

interface OS {
  isTablet: boolean;
  isPhone: boolean;
  isAndroid: boolean;
  isPc: boolean;
  isAlipay: boolean;
  isApp: boolean;
  isBookInApp: boolean;
  isWeChatBrower: boolean;
  isWeChatMiniProgramDesktop: boolean;
  isInvitePhone:boolean;
  isHarmonyOS:boolean
}

// 判断浏览器类型 暂时区分 安卓 iphone pc 平板
export const os: OS | undefined = (function () {
  if (!process.client) { return }
  const ua = navigator.userAgent
  console.log(ua)
  const lowerCaseUA = ua.toLowerCase()
  const isWindowsPhone = /(?:Windows Phone)/.test(ua)
  const isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone
  const isAndroid = /(?:Android)/.test(ua)
  const isFireFox = /(?:Firefox)/.test(ua)
  // const isChrome = /(?:Chrome|CriOS)/.test(ua)
  const isTablet =
    /(?:iPad|PlayBook)/.test(ua) ||
    (isAndroid && !/(?:Mobile)/.test(ua)) ||
    (isFireFox && /(?:Tablet)/.test(ua))
  const isPhone = (/(?:iPhone)/.test(ua) && !isTablet)
  const isPc = !isPhone && !isAndroid && !isSymbian // iPad中会被判断为true，使用时要小心
  const isAlipay = /(?:Alipay)/.test(ua)
  const isApp = /(?:kukecloudappwebview)/.test(ua)
  const isBookInApp = /(?:kukebook)/.test(ua)// 是否 图书在app内打开
  const isWeChatBrower = /(?:MicroMessenger)/.test(ua) // 是否微信浏览器
  const isInvitePhone = (/(?:iPhone|iPad)/.test(ua))
  const isWeChatMiniProgramDesktop = lowerCaseUA.includes('miniprogram') && (lowerCaseUA.includes('windowswechat') || lowerCaseUA.includes('macwechat')) // 是否微信小程序桌面版
  const isHarmonyOS = (/(?:OpenHarmony)/.test(ua))
  return {
    isTablet,
    isPhone,
    isAndroid,
    isPc,
    isAlipay,
    isApp,
    isBookInApp,
    isWeChatBrower,
    isWeChatMiniProgramDesktop,
    isInvitePhone,
    isHarmonyOS
  }
})()

/**
 * @param {String} path - 下载地址/下载请求地址。
 * @param {String} name - 下载文件的名字
 *
 */
export const downloadFile = (path:string, name:string) => {
  if (!path) { throw new Error('path 必传') }
  const regex = /\.[^.]+$/
  const match = path.match(regex)
  const suffix = match[0]
  const xhr = new XMLHttpRequest()
  xhr.open('get', path)
  xhr.responseType = 'blob'
  xhr.send()
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      // 如果是IE10及以上，不支持download属性，采用msSaveOrOpenBlob方法，但是IE10以下也不支持msSaveOrOpenBlob
      if ('msSaveOrOpenBlob' in navigator) {
        navigator.msSaveOrOpenBlob(this.response, name)
        return
      }
      // const blob = new Blob([this.response], { type: xhr.getResponseHeader('Content-Type') });
      // const url = URL.createObjectURL(blob);
      const url = URL.createObjectURL(this.response)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `${name}${suffix}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }
}

// 根据文件后缀名下载文件
export const downLoadFileRename = (url: string, name: string) => {
  const xhr = new XMLHttpRequest()
  xhr.open('GET', url, true)
  xhr.responseType = 'blob'
  xhr.onload = () => {
    // 下载文件
    if (xhr.status === 200) {
      const blob = new Blob([xhr.response], { type: 'application/octet-stream' })
      const link = document.createElement('a')
      // const lens = name.split('.') // 获取文件后缀

      const regex = /\.[^.]+$/
      const match = url.match(regex)
      let suffix = ''
      if (match) {
        suffix = match[0]
      }

      const nameRegex = /(.+\.)[^.]+$/
      const _name = name.replace('附件：', '')
      const nameMatch = _name.match(nameRegex)
      let fileName = ''

      if (nameMatch) {
        fileName = nameMatch[0]
      } else {
        fileName = name
      }

      link.href = window.URL.createObjectURL(blob)
      link.download = `${fileName}${suffix}`
      link.click()
      window.URL.revokeObjectURL(link.href)
    }
  }
  xhr.send()
}

/**
 * 是否是库课网校
 * @returns
 */
export const isKukeCloud = () => {
  const { appId, lesseeId } = useAppConfig()
  return appId === 1 && !lesseeId
}
