export type Status = 1 | 2 | 3 | 4 | 5

export interface coupons {
    couponName?:string
    couponId?:string
    couponTypeName?:string
    discountContent?:string
    expiredType?:number
    expiredStartTime?:string
    expiredEndTime?:string
    expiredDays?:number
    couponCount?:number
    allowanceCount?:number
    templateStatus?:number
    templateStatusName?:string
    activityStatusName?:string
    activityStatus?:number
    publishStartTime?:string
    publishEndTime?:string
  }
export interface Goods {
    goodsTitle?:string
    goodsPresentPrice?:string
    labelStyleList?:any[]
    discountContent?:string
    content?:number
    classTypes?:string
  }
export interface GiftPacks {
    goods?: any[]
    coupons?: coupons[]
    status: Status
    pcMarketingImageUrl: number
    wapMarketingImageUrl:string
    backgroundColorTop:string
    backgroundColorBottom:string
    fontColor:string
    oneClickColor:number
    activityName:string
    activityRule:string
  }
