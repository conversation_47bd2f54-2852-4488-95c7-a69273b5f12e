{
  "prettier.enable": false,
  // 每次保存的时候自动格式化
  "editor.formatOnSave": false,
  "eslint.enable": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "never",
    "source.fixAll.eslint": "explicit"
  },
  "[vue]": {
    // "editor.defaultFormatter": "Vue.volar"
  },
  "cSpell.words": [
    "Nuxt",
    "kukecoreuser",
    "defu",
    "kukefe",
    "kkutils",
    "requestAppkey",
    "ofetch",
    "consola",
    "composables",
    "kuke",
    "Testpaper",
    "tabbar",
    "pageid",
    "kefu"
  ],
  "sonarlint.connectedMode.project": {
    "connectionId": "kuke-web-sonar",
    "projectKey": "web_kuke-cloud_AY6ihffgMLuEGYYRlKHf"
  },
  "references.preferredLocation": "view",
  "workbench.activityBar.orientation": "vertical",
  "vue3snippets.enable-compile-vue-file-on-did-save-code": false
}