export interface ClaimInformation{
    lotteryRecordId:string
    prizeName?:string
    prizeMobile?:string
    prizeAddress?:string
    prizeCard?:string
    alipay?:string
    weChat?:string
    prizeAccountName?:string
    ads?:string[]|string
    prizeAddressProvince?:string
    prizeAddressCity?:string
    prizeAddressDistrict?:string
}
export interface userLotteryRes {
    winOrNot?:number
    levelName?:string
    level?:number
    prizeTitle?:string
    winningMessage?:string
    prizeInfoStatus?:number
    lotteryRecordId?:string
    prizeCover?:string
    prizeId:string
    prizeType:number
    prizeDelivery:{
        prizeName?:number
        prizeMobile?:number
        prizeAddress?:number
        prizeCard?:number
        alipay?:number
        weChat?:number
    }
}

export interface MyAwardRecordRes {
    winningTime:string
    levelName:string
    prizeTitle:string
    prizeInfoStatus:number
    id:string
}

/// 详情返回数据###⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇

interface Interdict {
    lotteryNumDay: number
    lotteryNumCount: number
    lotteryNumWinning: number
  }
export interface LotteryInterdict {
    id?: string
    lotteryUsers: number
    //* *部分用户 */
    // 购课用户
    buyGoods?: number
    buyGoodsList?: any[]
    buyGoodsInterdict: Interdict
    // 新用户
    newUser?: number
    userStartTime?: string
    userEndTime?: string
    time?: string[]
    newUserInterdict: Interdict
    // 导入用户
    loadUser?: number
    loadUserMobile?: string
    loadFileKey?: string
    loadUserInterdict: Interdict
    //* *全部用户 */
    allUserInterdict: Interdict
  }

export interface PrizeDelivery {
    prizeName?: number
    prizeMobile?: number
    prizeAddress?: number
    alipay?: number
    prizeCard?: number
    weChat?: number
  }

export interface Prize {
    id: string
    prizeCover:string
  }

// 第一步的信息
export interface StepOneRaffle {
  id:string
    lotteryTitle: string
    activityRemark: string
    backgroundPic: string
    backgroundColorTop: string
    backgroundColorBottom: string
    status: string
  }

// 第二步的信息
export interface StepTwoRaffle {
    lotteryGoodsList: Prize[]
  }

export interface VirtualRemark {
    virtualMobile:string
    virtualPrizeLevel:string
    virtualPrize:string
  }

// 第三步的信息
export interface StepThreeRaffle {
    shareCount: number
    allShareCount: number
    userLotterySum: number
    virtualRemarkList: VirtualRemark[]
  }

export interface raffleRes extends StepOneRaffle, StepTwoRaffle, StepThreeRaffle {
    id: string
  }
  // ### ↑↑↑↑↑↑↑↑↑↑↑↑
