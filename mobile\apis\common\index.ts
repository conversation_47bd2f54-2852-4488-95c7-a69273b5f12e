import { hash } from 'ohash'
import { ApisCommon } from '../../../base/apis/common'
import type { SendCodeParams, CheckCodeParams, ShareInfoDTO, FetchAddOrdersBehaviorRecordParams } from './types'

enum CommonApi {
  sendCode = '/kukecoreuser/wap/sms/sendCode', // 发送验证码
  checkCode = '/kukecoreuser/app/user/checkCode', // 验证验证码
  shareDetail = '/kukemarketing/wap/pageMaster/promotionDetailsWX', // 推广分享信息
  getWxShareInfo = '/kukecoreuser/wap/woa/oauth/wxShareGetParams', // 获取分享appid
  getClientCategoryProt = '/kukebasesystem/ksProduct/getClientCategoryProt', // 获取专业分类列表
  getURLSchemeNews = '/kukemarketing/wap/pageMaster/getURLSchemeNews', // 跳转小程序
  // TODO 该接口可使用base下fetchAddOrdersBehaviorRecord
  fetchAddOrdersBehaviorRecord = '/kukecluedispatch/KcdBehaviorRecord/addOrderBehaviorRecordProt',
}

// 获取验证码
export async function sendCode (body: SendCodeParams) {
  const res = await useHttp<void>(CommonApi.sendCode, {
    method: 'post',
    body,
    encrypt: 'AES'
  })
  if (!res.error.value) {
    Message(res.data.value!.msg)
  }

  return res
}
// 验证验证码
export async function checkCode (body: CheckCodeParams) {
  return useHttp<void>(CommonApi.checkCode, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
// 获取oss相关参数
export async function fetchOSSConfig () {
  return useHttp<Object>('/kukeopen/oss/stsToken', {
    method: 'post',
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}
// 获取推广分享信息
export async function getShareInfo (body: ShareInfoDTO) {
  return useHttp<any>(CommonApi.shareDetail, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
export async function getShareInfoV2 (body: ShareInfoDTO) {
  const _api = CommonApi.shareDetail
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    key,
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
// 获取分享appid
export async function getWxShareConfig (body: any) {
  return useHttp<any>(CommonApi.getWxShareInfo, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

// 获取专业分类列表
export async function getClientCategoryProt () {
  return useHttp<any>(CommonApi.getClientCategoryProt, {
    method: 'post',
    transform: (res: any) => res.data,
  })
}

// 获取专业分类列表
export async function getURLSchemeNews (body:any) {
  return useHttp<any>(CommonApi.getURLSchemeNews, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *新增订单行为记录
 * @param body
 * @returns
 */
export async function fetchAddOrdersBehaviorRecord (body:FetchAddOrdersBehaviorRecordParams) {
  return useHttp<any>(CommonApi.fetchAddOrdersBehaviorRecord, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *获取基础数据枚举值
 * @param sceneUsedId 使用场景值（见基础配置-使用场景，不传获取全部）
 * @returns
 */
export async function fetchGetEnumList (sceneUsedId: string, status: number[] = [1]) {
  return useHttp<any>(ApisCommon.fetchGetEnumList, {
    method: 'post',
    body: { status, sceneUsedId },
    transform: (res: any) => res.data,
  })
}
