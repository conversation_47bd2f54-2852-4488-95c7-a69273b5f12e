<template>
  <span
    :class="{
      'lc-sprite': true,
      'is-wap': type==='WAP',
      'is-pc': type==='PC',
    }"
    :style="styleObj"
  />
</template>

<script setup lang="ts">
const props = defineProps({
  x: {
    type: Number,
    default: 0
  },
  y: {
    type: Number,
    default: 0
  },
  type: {
    type: String,
    default: 'WAP' // PC WAP
  },
})
const styleObj = computed(() => {
  if (props.type === 'WAP') {
    return {
      'background-position-x': isDefined(props.x) ? px2rem(props.x!) : undefined,
      'background-position-y': isDefined(props.y) ? px2rem(props.y!) : undefined
    }
  }
  return {
    'background-position-x': isDefined(props.x) ? (props.x + 'px') : undefined,
    'background-position-y': isDefined(props.y) ? (props.y + 'px') : undefined
  }
})
</script>

<style lang="scss">
.lc-sprite{
  background-repeat: no-repeat;
  &.is-wap{
    //
    background-size: 51px 298px;
    background-image: url('../../assets/lc/sprite-m.png');
  }
  &.is-pc{
    background-size: 54PX 306PX;
    background-image: url('../../assets/lc/sprite-pc.png');
  }
}
</style>
