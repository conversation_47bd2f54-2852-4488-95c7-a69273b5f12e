export type defaultTrigger = 'onMounted' | 'onBeforeUnmount'
type IOpts = { defaultTrigger:defaultTrigger[] }

export function usePageTrack (opts: IOpts = { defaultTrigger: ['onMounted'] }) {
  // TODO state
  const { appId } = useAppConfig()
  const { productSide } = useGetProductSide()
  const browsingTime = ref(0)
  const timer = ref()
  const route = useRoute()

  const requestPath = '/kukemarketing/customPage/statisticalViews'
  const trigger = async (extraBody = {}) => {
    if (route.name !== 'cp-id') { return }
    const body = {
      ...extraBody,
      // userMobile: userStore.getUserInfo?.mobile,
      clientType: productSide,
      productId: appId,
      id: route.params.id,
    }
    if (browsingTime.value) {
      body.browsingTime = browsingTime.value
    }
    clearInterval(timer.value)
    return await useHttp<any>(requestPath, { body })
  }
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  const timerCreate = () => setInterval(() => {
    browsingTime.value++
  }, 1000)

  onMounted(async () => {
    timer.value = timerCreate()
    await nextTick()
    if (opts?.defaultTrigger.includes('onBeforeUnmount')) {
      window.addEventListener('beforeunload', onBeforePageLeave)
    }
    document.addEventListener('visibilitychange', visibilitychangeFn)
    if (opts?.defaultTrigger.includes('onMounted')) {
      await trigger({ triggerType: 'onMounted' })
    }
  })
  // onBeforeUnmount(() => { })
  onBeforeRouteLeave(async () => {
    if (opts?.defaultTrigger.includes('onBeforeUnmount')) {
      await trigger({ triggerType: 'onBeforeUnmount' })
    }
  })

  const onBeforePageLeave = () => {
    if (opts?.defaultTrigger.includes('onBeforeUnmount')) {
      trigger({ triggerType: 'beforeunload' })
    }
  }
  const visibilitychangeFn = () => {
    // if (opts?.defaultTrigger.includes('visibilitychange')) {
    const isHidden = document.hidden
    console.log(document.visibilityState)
    if (isHidden) {
      // trigger({ triggerType: 'visibilitychange' })
      clearInterval(timer.value)
    } else {
      timer.value = timerCreate()
    }
    // }
  }

  // TODO 验证
  onScopeDispose(() => {
    console.log(' [ onScopeDispose ] ')
  })
  return { trigger, browsingTime, timerCreate }
}
/**
 *
 * @returns
 */
export function useGetProductSide () {
  // TODO state
  const { isXcx, clientType, isKukeCloudAppWebview } = useAppConfig()
  const productSide = ref(clientType)
  if (isXcx) {
    productSide.value = CLIENT_TYPE.XCX
  } else if (isKukeCloudAppWebview) {
    productSide.value = CLIENT_TYPE.APP
  }
  return { productSide }
}
