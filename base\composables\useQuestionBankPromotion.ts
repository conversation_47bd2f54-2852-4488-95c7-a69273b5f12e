import { useUserStore } from '../../mobile/stores/user.store'

interface PromotionType {
  landPageState: number; // landPageState 落地页状态 1 开启；  0 关闭
  doLandPage: number; // doLandPage 落地页是否填写 1 填过；  0 未填过
  landLink: string; // landLink 落地页链接
}

export const useQuestionBankPromotion = () => {
  const { isXcx } = useAppConfig()
  const handleJumpPromotionPage = (promotion: PromotionType) => {
    const { landPageState, doLandPage, landLink } = promotion
    if (landPageState === 1 && doLandPage === 0) {
      // 跳转落地页
      if (isXcx) {
        const userStore = useUserStore()
        userStore.jumpAppletWebFullPage(landLink)
      } else {
        window.location.href = landLink
      }
      return false
    }
    return true
  }

  return {
    handleJumpPromotionPage
  }
}
