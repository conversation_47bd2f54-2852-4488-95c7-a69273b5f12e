/**
 *
 * ```
 * .block {}
 * .block__element {}
 * .block--modifier {}
 * .block__element--modifier {}
 * ```
 * ```
 * const bem = useBem('kkc-card')
 * bem() // .kkc-card
 * bem(null, 'border') // .kkc-card--border
 * bem('header') // .kkc-card__header
 * bem('header', 'action') // .kkc-card__header--action
 * ```
 *
 * @param block
 * @returns
 */
export const useBem = (block: string) => {
  return (element = '', modifier = '') => {
    if (!element && !modifier) {
      return block
    }
    if (!modifier) {
      return `${block}__${element}`
    }
    if (!element) {
      return `${block}--${modifier}`
    }
    return `${block}__${element}--${modifier}`
  }
}
