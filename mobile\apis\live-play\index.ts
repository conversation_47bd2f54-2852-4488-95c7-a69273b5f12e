import type { LiveInfoResponse, LiveRoomParams, LiveRoomResponse } from './types'

enum livePlayApi {
    liveInfo = '/kukecoregoods/wap/wechatMinLive/getLastInfo',
    getUrlScheme = '/kukeopen/wechat/getUrlScheme'
}

/**
 * 获取直播信息
 * @param body
 */
export async function getLiveInfo () {
  return useHttp<LiveInfoResponse>(livePlayApi.liveInfo, {
    method: 'post',
    transform: (res: any) => res.data,
  })
}

/**
 * 获取直播间url
 * @returns
 */
export async function getUrlScheme (body : LiveRoomParams) {
  return useHttp<LiveRoomResponse>(livePlayApi.getUrlScheme, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
