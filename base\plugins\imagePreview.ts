import { DirectiveBinding, createVNode, render } from 'vue'
import { ElImageViewer } from 'element-plus'

// 工具函数：解析字符串中的图片 URL
function parseImageUrls (input: string): string[] {
  const urlRegex = /(https?:\/\/[^\s]+(?:jpg|jpeg|png|gif))/g // 正则表达式用于匹配图片 URL
  const urls = input.match(urlRegex) // 使用正则表达式匹配图片 URL
  return urls || [] // 如果匹配到图片 URL，则返回匹配结果，否则返回空数组
}

const imagePreview = {
  mounted (el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
    el.style.cursor = 'pointer' // 设置鼠标样式为手型，表示可点击

    el.addEventListener('click', (event) => {
      // 阻止触发同一个元素的其他点击事件
      // event.stopImmediatePropagation()
      // 获取所有图片的 URL
      let urlList: string[] = []
      if (Array.isArray(binding.value)) {
        urlList = binding.value
      } else {
        urlList = parseImageUrls(binding.value)
      }

      if (urlList.length === 0) {
        console.warn('No valid image URLs found.')
        return
      }

      // 确保点击的是图片元素
      const target = event.target as HTMLElement
      if (target.tagName !== 'IMG') {
        console.warn('Clicked element is not an image.')
        return
      }

      // 获取点击的图片 URL
      const clickedImageUrl = (target as HTMLImageElement).src
      // 计算点击的图片在列表中的索引
      const initialIndex = urlList.findIndex(url => url === clickedImageUrl)
      if (initialIndex === -1) {
        console.warn('Clicked image URL not found in the list.')
        return
      }

      // 阻止事件冒泡和默认行为
      event.stopPropagation()
      event.preventDefault()

      const vnode = createVNode(ElImageViewer, {
        urlList,
        initialIndex,
        onClose: () => {
          render(null, container) // 关闭图片预览时，将组件从 DOM 中移除
          document.body.removeChild(container) // 同时移除容器
        },
      })

      const container = document.createElement('div') // 创建一个容器元素
      document.body.appendChild(container) // 将容器添加到 body 中
      render(vnode, container) // 在容器中渲染图片预览组件
    }, { capture: true })
  },
}

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('imagePreview', imagePreview) // 在 Nuxt.js 中注册自定义指令
})
