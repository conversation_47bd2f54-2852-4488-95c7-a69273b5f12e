
## 部署指南

https://alidocs.dingtalk.com/i/nodes/MNDoBb60VLrMknQef3gpjpmR8lemrZQ3?utm_scene=team_space

### 方式2: node

```bash
# mobile
node ./mobile/.output/server/index.mjs
# pc
node ./pc/.output/server/index.mjs
```

### 方式3: pm2 (待定) 

```bash
# 切换 cd 对应目录
cd [mobile|pc]
pm2 start ecosystem.config.cjs --env prod
pm2 start ecosystem.config.cjs --env dev
pm2 start ecosystem.config.cjs --env staging
pm2 start ecosystem.config.cjs --env pre
pm2 start ecosystem.config.cjs --env pressure
```

### 3. 预览:

```bash
# pc
curl http://localhost:3000
# mobile
curl http://localhost:3006
```