export interface ILearnTargetInfo {
    enable?: Boolean
    // 1. 配置关闭 - 首页的学习目标
    idOfDefaultHome?: string | null
    // 2. 配置开启 - 默认的学习目标
    idOfDefault?: string | null
    // 4. 用户选择的学习目标
    idOfCurrentUser?: string | null
    // TODO 另需处理:
    // 1. (登录人绑定)单个学习目标 【被关闭】 或 【失效(有下级)】
    // 2. (非登录)    单个学习目标 【被关闭】 或 【失效(有下级)】
}

/**
 * @example
 * ```
 * 接口需要传递 pageMasterId 的，取值规则
 * const { usableId } = useLearnTarget()
 * ```
 * */
export const useLearnTarget = () => {
  const learnTargetInfo = useState<ILearnTargetInfo>('learnTargetInfo', () => ({ }))

  const localId = useCookie<string>('LT')
  // setLocalId 即将废弃
  const setLocalId = (payload:string) => {
    console.log('setLocalId: ', payload)
    useCookie<string>('LT').value = payload
  }

  const setLearnTargetId = (name: keyof Omit<ILearnTargetInfo, 'enable'>, value:string | null) => {
    learnTargetInfo.value[name] = value
  }

  const setLearnTargetEnable = (value:boolean) => {
    learnTargetInfo.value.enable = value
    // learnTargetInfo.value.enable = false
  }

  const usableId = computed(() => {
    if (learnTargetInfo.value.idOfCurrentUser) {
      return learnTargetInfo.value.idOfCurrentUser
    } else if (learnTargetInfo.value.enable) {
      return learnTargetInfo.value.idOfDefault
    } else {
      return learnTargetInfo.value.idOfDefaultHome
    }
  })

  const lastPageId = computed(() => {
    console.log(learnTargetInfo.value.idOfCurrentUser, 'learnTargetInfo.value.idOfCurrentUser')

    if (learnTargetInfo.value.idOfCurrentUser) {
      return learnTargetInfo.value.idOfCurrentUser
    } else {
      return undefined
    }
  })
  // TODO
  // console.log({
  //   ...learnTargetInfo.value,
  //   usableId: usableId.value
  // })
  /** @deprecated */
  const learnTargetNodes = useState<any[]>('learnTargetNodes', () => [])
  /** @deprecated */
  const getNames = computed(
    () => (start?: number, end?: number) =>
      learnTargetNodes.value
        .slice(start, end)
        .map(v => v.categoryName)
        .join(' · '),
  )

  return {
    LT_LOCAL_KEY,
    LT_SEARCH_KEY,
    learnTargetInfo,
    setLearnTargetId,
    usableId,
    localId,
    setLocalId,
    setLearnTargetEnable,
    lastPageId,
    learnTargetNodes,
    getNames,
  }
}

// dataSource:Ref<any>
export const useLearnTargetTree = () => {
  // const nodePaths = ref()
  const getFlatTree = <T extends any[]>(tree:T, pid: string|null, result:T) => {
    if (!tree) { return result }
    tree.forEach((node) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { subcategoryList: children, catalogueCode, ...o } = node
      result.push({
        ...o,
        parentId: pid
      })
      if (children?.length) {
        getFlatTree(children, node.usedId, result)
      }
    })
    return result
  }
  const getChildrenById = <T extends any[]>(list:T, pid:string | null) => {
    return list.filter(node => node.parentId === pid)
  }
  const getNodeById = <T extends any[]>(list:T, id:string) => {
    return list.find(node => node.usedId === id)
  }
  const getNodeByPageId = <T extends any[]>(list:T, pageId:string | null, key = 'pageMasterId') => {
    if (!pageId) { return }
    return list.find(node => node[key] === pageId)
  }

  return {
    // flatList,
    // nodePaths,
    // learnTargetList,
    getFlatTree,
    getChildrenById,
    getNodeById,
    getNodeByPageId,
    // getNodePathsById,
    // findNodeId,
  }
}
