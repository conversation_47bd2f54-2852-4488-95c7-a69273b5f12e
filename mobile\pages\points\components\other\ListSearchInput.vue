<template>
  <div
    class="flex-1 flex items-center border-[2px] border-[#F5F6F9] focus-within:border-[var(--kkc-brand)] rounded-[20px] h-[72px] pl-[16px] pr-[8px] bg-white"
    :class="{ 'focus-within:border-[var(--kkc-brand)]': keyword }"
  >
    <div class="relative flex items-center w-full mr-[4px]">
      <KKCIcon name="icon-sousuo1" :size="38" class="pt-[4px] mr-[4px] text-[#999]" />
      <input
        ref="inputRef"
        v-model="keyword"
        maxlength="50"
        :placeholder="placeholder"
        class="w-full placeholder:text-[#999] placeholder:text-[28px] placeholder:leading-[48px] placeholder:font-normal"
        @keyup.enter="handleSearchPointsList"
      >
      <KKCIcon
        v-show="keyword"
        :size="42"
        name="icon-shurukuang-qingchu"
        class="mr-[18px] text-[#111]"
        @click="handleClearValue"
      />
    </div>
    <button
      class="bg-[var(--kkc-brand)] rounded-[12px] w-[104px] h-[56px] flex items-center justify-center"
      @click="handleSearchPointsList"
    >
      <p class="text-[28px] font-medium leading-1 text-white">
        搜索
      </p>
    </button>
  </div>
</template>

<script setup lang='ts'>

interface SearchInput {
  // 输入框的默认提示信息
  placeholder?: string
  // 外部传入的搜索关键字（用于状态恢复）
  modelValue?: string
}

const props = withDefaults(defineProps<SearchInput>(), {
  placeholder: '搜索商品名称',
  modelValue: ''
})

const emits = defineEmits<{
  clear: []
  reset: [keyword: string]
  'update:modelValue': [keyword: string]
}>()

// 注入输入框的选择器
const inputRef = ref<InstanceType<typeof HTMLInputElement>>()
// 初始化选中数据框
const initInputFocus = () => inputRef.value?.focus()
// 初始化选中
onMounted(initInputFocus)

// 搜索栏输入的数据
const keyword = ref<string>('')

// 监听外部传入的值变化，同步到内部状态
watch(() => props.modelValue, (newValue) => {
  if (newValue !== keyword.value) {
    keyword.value = newValue || ''
  }
}, { immediate: true })

// 监听内部状态变化，同步到外部
watch(() => keyword.value, (value) => {
  emits('update:modelValue', value)
  if (!value) {
    emits('clear')
  }
})

// 清除输入框的数据
const handleClearValue = () => (keyword.value = '')

// 点击搜索查询数据
const handleSearchPointsList = () => {
  if (!keyword.value) {
    Message('请先输入搜索词哦')
    return
  }
  // 取消输入框的选中状态
  nextTick(() => {
    inputRef.value?.blur()
  })

  emits('reset', keyword.value)
}

</script>
