<template>
  <div class="w-full min-h-[100vh] flex justify-center items-center">
    <div
      class="kkc-offline flex justify-center items-center flex-col"
    >
      <img src="https://oss.kuke99.com/kukecloud/static/offline.png" alt="" class="kkc-offline__img">
      <div class="kkc-offline__text text-[#666]">
        网络已断开，请联网后刷新页面~
      </div>
      <div class="flex kkc-offline__btns">
        <button
          v-if="$route.path !== '/'"
          class="kkc-offline__custom text-[#111]"
        >
          <a href="/">返回首页</a>
        </button>
        <button
          class="kkc-offline__btn text-[#fff]"
          @click="reload"
        >
          刷新页面
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const reload = () => location.reload()
</script>

<style lang="scss" scoped>
.kkc-offline {
  &__img {
    width: 210px;
  }
  &__btns {
    margin-top: 24px;
    border-radius: 8px;
    button {
      border-radius: 8px;
      &:nth-of-type(2) {
        margin-left: 12px;
      }
    }
  }
  &__text {
    height: 17px;
    font-size: 14px;
    margin-top: 12px;
  }
  &__btn {
    height: 36px;
    font-size: 14px;
    padding: 0 12px;
    background-color: var(--kkc-brand, #EB2330);
  }
  &__custom{
    height: 36px;
    font-size: 14px;
    padding: 0 12px;
    background-color: #F2F2F2;
  }
}
</style>
