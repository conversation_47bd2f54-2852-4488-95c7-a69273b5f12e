# Tag 标签

### 基本使用

由`type`属性来选择`tag`的类型，支持 `default`、`success`、`warning`、`danger`类型，默认为 `default`。

```vue demo
<template>
  <div>
    <KKCTag>标签一</KKCTag>
    <KKCTag type="success">标签二</KKCTag>
    <KKCTag type="warning">标签三</KKCTag>
    <KKCTag type="danger">标签四</KKCTag>
    <KKCTag type="danger" color="#fff" bgColor="#c642e7" borderColor="#c642e7"
      >自定样式</KKCTag
    >
  </div>
</template>
```

### 不同尺寸

`Tag` 组件提供除了默认值以外的三种尺寸，可以在不同场景下选择合适的按钮尺寸。

```vue demo
<template>
  <div>
    <KKCTag size="large">大 h64px </KKCTag>
    <KKCTag size="middle">中 h56px </KKCTag>
    <KKCTag size="small">小 h48px </KKCTag>
    <KKCTag size="default">默认 h40px</KKCTag>
    <KKCTag size="mini">超小 h36px </KKCTag>
  </div>
</template>
```

### 渐变色使用

```vue demo
<template>
  <div>
    <KKCTag
      size="large"
      type="success"
      color="#fff"
      bg-color="linear-gradient(to right, #ff6034, #ee0a24)"
    >
      大号
    </KKCTag>
  </div>
</template>
```

## API

### Tag Props

| 参数    | 类型   | 说明                                 |
| ------- | ------ | ------------------------------------ |
| type    | string | 类型，success/default/warning/danger |
| color   | string | 字体颜色                             |
| bgColor | string | 背景色                               |
| size    | string | 尺寸，large / default/small / mini   |
