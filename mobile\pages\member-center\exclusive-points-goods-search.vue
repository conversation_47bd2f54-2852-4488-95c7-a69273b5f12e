<template>
  <PageLayout :header-height="88">
    <template #header>
      <PageFixedTopLayout :top="0">
        <div class="h-full w-full flex items-center px-[24px] bg-[#F5F6F9]">
          <KKCIcon
            v-if="!isXcx"
            name="icon-com_return"
            :size="48"
            @click="goBack"
          />

          <PointsListSearchInput @clear="handleClearSearch" @reset="handleListSearch" />
        </div>
      </PageFixedTopLayout>
    </template>

    <PointsListLoad
      v-show="list.length"
      ref="listLoadRef"
      class="bg-white h-full"
      :up-load="handleUpLoad"
      :pull-refresh="handlePullRefresh"
    >
      <ul class="grid grid-cols-2 gap-x-[22px] gap-y-[48px] p-[24px]">
        <li v-for="item in list" :key="item.goodsId">
          <PointsRedeemGoodsItem :goods="item" :height="226" />
        </li>
      </ul>
    </PointsListLoad>

    <KKCEmpty v-show="isLoaded && !list.length">
      <p>暂时没有商品，快去看看其他的商品吧~</p>
    </KKCEmpty>
  </PageLayout>
</template>

<script setup lang='ts'>
// components
import PageLayout from '~/pages/points/components/layout/PageLayout.vue'
import PointsListLoad from '~/pages/points/components/other/ListLoad.vue'
import PageFixedTopLayout from '~/pages/points/components/layout/PageFixedTopLayout.vue'
import PointsListSearchInput from '~/pages/points/components/other/ListSearchInput.vue'
import PointsRedeemGoodsItem from '~/pages/points/components/goods/PointsRedeemGoodsItem.vue'
// hooks
import { usePointsInfo, usePointsMallList } from '~/pages/points/hooks/index'
// types
import type { Props } from '~/pages/points/types/index'

// 全局app信息
const { isXcx } = useAppConfig()

// 获取 router
const router = useRouter()
// 返回上一级页面
const goBack = debounceFunc(router.back, 800, true)

// 获取路由信息
const { memberGoodsId = '' } = useRoute().query
// 获取积分兑换列表
const { list, isLoaded, getPointsList } = usePointsMallList()
// 列表刷新的选择器实例
const listLoadRef = ref<InstanceType<typeof PointsListLoad>>()
// 下拉重新加载数据
const handlePullRefresh = async (params: Props) => getList(params)
// 上滑重新加载数据
const handleUpLoad = async (params: Props) => getList(params)
// 查询列表信息
const getList = (params: Props) => getPointsList({ ...params, memberGoodsId, sortType: 1, title: keyword.value })

// 搜索项的内容
const keyword = ref<string | undefined>(undefined)
// 清空搜素内容
const handleClearSearch = () => {
  list.value = []
  isLoaded.value = false
  keyword.value = undefined
}
// 根据搜索项刷新列表
const handleListSearch = (value: string) => {
  keyword.value = value

  listLoadRef.value?.handleResetLoad()
}

// 积分标题，在根据运营中心的配置动态展示
const { points, getPointsCenterStatus } = usePointsInfo()
// 加载数据
getPointsCenterStatus()

/* ----------------- 页面元数据配置信息 --------------------  */
// 当前组件的原数据配置
definePageMeta({ ignoreLearnTarget: true })
// 把积分的基础信息挂挂载到 provide
provide('points', { points })
</script>
