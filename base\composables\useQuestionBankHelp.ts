export const useQuestionBankHelp = () => {
  const message = [
    {
      id: 1,
      q: '问：功能模块或单个试卷解锁后，是否支持退改?',
      a: '答：购买功能模块或单个试卷后无法退改，已缴纳的费用不会返还，请您谨慎购买'
    },
    {
      id: 2,
      q: '问：购买单个功能模块后，使用其他模块需再次付款解锁吗？',
      a: '答：需要，每个模块属于独立模块，价格及有效期均不同，请谨慎下单'
    },
    {
      id: 3,
      q: '问：解锁后可享受什么服务?',
      a: '答：您所购买的功能模块对应科目或单个试卷，在有效期内支持无限次刷题(特定考试试卷除外)'
    },
    {
      id: 4,
      q: '问：有效期是如何计算的?',
      a: '答：用户享受从购买之时起连续N个月(根据用户实际购买的月数进行计算)的做题服务，到期后将自动失效'
    },
    {
      id: 5,
      q: '问：功能模块或单个试卷过期后，会自动续费吗?',
      a: '答：系统不会自动续费，若还想继续享受该模块的刷题功能，可以再次进行购买'
    },
    {
      id: 6,
      q: '问：我还有其他问题怎么办?',
      a: '答：若仍有其他问题，欢迎咨询在线客服'
    }
  ]

  return {
    message
  }
}
