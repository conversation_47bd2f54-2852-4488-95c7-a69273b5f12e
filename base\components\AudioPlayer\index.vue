<template>
  <div class="audio" @click.stop @touchstart.stop @touchmove.stop @touchend.stop>
    <KKCIcon
      :class="['cursor-pointer', iconSize !== 24 ? 'audio-icon' : '']"
      :name="isPause ? iconName : playingIcon"
      :color="isCanPlay ? 'var(--kkc-brand)' : '#999999'"
      :size="iconSize"
      @click.stop="onClickBtn"
    />
    <div class="audio-text mx-[12px]">
      {{ dayjs(currentTime).format("mm:ss") }}
    </div>
    <input
      :id="uuid"
      type="range"
      class="audio-process"
      :value="currentTime"
      min="0"
      :max="durationTime"
      step="1"
    >
    <div class="audio-text ml-[12px] mr-[12px]">
      {{ dayjs(durationTime).format("mm:ss") }}
    </div>
    <div v-if="showDel" class="audio-text cursor-pointer" @click="onDel">
      删除
    </div>
    <!-- 音频 -->
    <audio :id="audioId" class="hidden" controls preload="auto" crossorigin="anonymous">
      <source :src="url">
    </audio>
  </div>
</template>

<script setup lang="ts">
import { buildUUID } from '../../utils/uuid'
import { addAudioUniquely, pauseAudio, removeAudio } from '../../utils/audio'
const { $dayjs: dayjs } = useNuxtApp()

const props = withDefaults(
  defineProps<{
    // 音频资源
    url: string;
    showDel?: boolean;
    bgColor?: string;
    iconName?: string;
    playingIcon?: string;
    iconSize?: number;
  }>(),
  {
    iconName: 'icon-pczanting',
    playingIcon: 'icon-xiangqing-zanting',
    iconSize: 24,
  }
)

const emits = defineEmits<{
  (e: 'del', url: string): void;
}>()

const uuid = computed(() => 'process_' + buildUUID())
const audioId = computed(() => 'audio_' + buildUUID())

// 开始时间
const currentTime = ref(0)
// 音频总时长
const durationTime = ref(0)
// 是否可播放
const isCanPlay = ref(false)
// 是否暂停
const isPause = ref(true)

const slider = ref()
const audioPlaer = ref<HTMLAudioElement | null>()
// 绑定监听事件
const addListener = () => {
  checkCanplay()
  // addAudio(audioPlaer.value as HTMLAudioElement)
  // audioPlaer.value?.addEventListener('canplay', canplay)
  audioPlaer.value?.addEventListener('play', play)
  audioPlaer.value?.addEventListener('pause', pause)
  audioPlaer.value?.addEventListener('ended', ended)
  audioPlaer.value?.addEventListener('timeupdate', timeupdate)
}

const offListener = () => {
  // audioPlaer.value?.removeEventListener('canplay', canplay)
  audioPlaer.value?.removeEventListener('play', play)
  audioPlaer.value?.removeEventListener('pause', pause)
  audioPlaer.value?.removeEventListener('ended', ended)
  audioPlaer.value?.removeEventListener('timeupdate', timeupdate)
}

// const canplay = () => {
//   isCanPlay.value = true
//   durationTime.value = audioPlaer.value!.duration * 1000
// }

const play = () => {
  // 开始播放更新isPause状态
  isPause.value = false
}

const pause = () => {
  // 开始播放更新isPause状态
  isPause.value = true
}

const ended = () => {
  // 音频播放结束
  currentTime.value = 0
  isPause.value = true
  handleSliderFillBg(currentTime.value)
}

const timeupdate = () => {
  // 更新进度条
  if (durationTime.value) {
    durationTime.value = audioPlaer.value!.duration * 1000
    currentTime.value = audioPlaer.value!.currentTime * 1000
  }
  // 填充slider
  handleSliderFillBg(currentTime.value)
}

const onClickBtn = () => {
  if (!isCanPlay.value) {
    return false
  }
  try {
    if (isPause.value) {
    // 暂停其他播放
      pauseAudio()
      // 开始播放
      const playPromise = audioPlaer.value?.play()
      // 处理播放承诺
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.error('播放失败:', error)
          // 自动播放策略可能阻止播放
          isPause.value = true
          // 在移动端，可能需要重新加载音频
          audioPlaer.value?.load()
        })
      }
    } else {
      audioPlaer.value?.pause()
    }
  } catch (error) {
    console.error('播放控制错误:', error)
    isPause.value = true
  }
}

// 滑动事件
const handleChanging = () => {
  try {
    // 暂停音频
    pauseAudio()
    // 填充slider
    handleSliderFillBg(parseFloat(slider.value.value))
  } catch (error) {
    console.log(error)
    audioPlaer.value?.load()
  }
}

// change事件处理播放时间设置
const handleChanged = () => {
  console.log('handleChanged', slider.value.value)
  sweek(slider.value.value)
}

// slider填充
const handleSliderFillBg = (currValue: number) => {
  // 滑块处理
  const value = ((currValue / durationTime.value) * 100).toFixed(2)
  // 填充色值
  slider.value.style.background = `linear-gradient(to right, var(--kkc-brand) ${value}%, #dddddd ${value}%)`
}

const sweek = (value: number) => {
  console.log('sweek', value)
  // 设置播放时间
  audioPlaer.value!.currentTime = value / 1000
  currentTime.value = value
  console.log('sweek currentTime', currentTime.value)
  audioPlaer.value?.play()
}

const onDel = () => {
  // 删除音频
  emits('del', '')
}

const checkCanplay = async () => {
  try {
    if (typeof AudioContext !== 'undefined') {
      const audioContext = new AudioContext()
      // 获取音频资源
      const file = await fetch(props.url)
      // 转换为arrayBuffer
      const arrayBuffer = await file.arrayBuffer()
      // 解码音频，获取信息
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

      // 延迟一下
      await new Promise(resolve => setTimeout(resolve, 100))

      // 设置是否可播放以及音频时长
      isCanPlay.value = true
      durationTime.value = audioBuffer.duration * 1000
      addAudioUniquely(audioPlaer.value as HTMLAudioElement)
    } else {
      // 降级方案：使用 HTML5 <audio> 元素
      const audioElement = new Audio(props.url)
      audioElement.addEventListener('loadedmetadata', async () => {
        // 延迟一下
        await new Promise(resolve => setTimeout(resolve, 100))

        isCanPlay.value = true
        durationTime.value = audioElement.duration * 1000
        addAudioUniquely(audioPlaer.value as HTMLAudioElement)
      })
      audioElement.addEventListener('error', (error) => {
        console.error('Audio element error:', error)
      })
    }
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  nextTick(() => {
    slider.value = document.querySelector(`#${uuid.value}`)
    // 监听input事件，实现slider 滑动效果
    slider.value.addEventListener('input', handleChanging)
    slider.value.addEventListener('change', handleChanged)
    // 创建音频
    audioPlaer.value = document.querySelector(
      `#${audioId.value}`
    ) as HTMLAudioElement
    // 添加监听事件
    addListener()
  })
})

onBeforeUnmount(() => {
  // 暂停音频
  audioPlaer.value?.pause()
  slider.value.removeEventListener('input', handleChanging)
  slider.value.removeEventListener('change', handleChanged)
  // 移除监听事件
  offListener()
  // 移除缓存数据
  removeAudio(audioPlaer.value as HTMLAudioElement)
  audioPlaer.value = null
})
</script>

<style lang="scss" scoped>
.audio {
  @apply h-[34px] flex px-[12px] justify-between items-center rounded-[6px];

  &-icon {
    @apply bg-[#ffffff] rounded-[50%];
  }

  &-process {
    @apply w-full h-[4px] flex-1 m-0 bg-[#dddddd] border-none rounded-[2px] cursor-pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;

    &:focus {
      outline: none;
    }

    &::-webkit-slider-thumb {
      @apply w-[10px] h-[10px] leading-[5px] bg-[#ffffff] rounded-[50%] cursor-pointer;
      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
      outline: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }
  }

  &-text {
    @apply text-[12px] text-[#333] font-[500];
  }
}
</style>
