import type { UseFetchOptions } from 'nuxt/app'
import type { FetchContext } from 'ofetch'
import { defu } from 'defu'
import type { KeysOf } from 'nuxt/dist/app/composables/asyncData'
import { NetCode } from '../constants/index'
import { isIframe } from '../utils/is'
import { useUserStore } from '~/stores/user.store'
// import { useLearnStore } from '~/stores/learn.store'
// type FetchType = typeof useFetch
// type ReqType = Parameters<FetchType>[0]
// type FetchOptions = Parameters<FetchType>[1]

// 扩展options类型定义
type CustomOptionsType = {
  isShowLoading?: boolean
  // 请求参数加密的类型 目前仅支持 AES
  encrypt?: string
  // 是否展示遮罩
  isShowMask?: boolean
}

export type HttpOption<T, ResT> = UseFetchOptions<IResponse<T>, ResT>;
// const logger = consola.withTag('useHttp')
// import { useLogger } from '@nuxt/kit'
// const logger = useLogger('useHttp')

// TODO 内存泄漏可疑点: 可变全局变量
const loadingCount = ref(0)
const addLoading = (isShowMask: boolean = false) => {
  loadingCount.value++
  Loading(true, '', isShowMask, 'transparent')
}
const closeLoading = () => {
  loadingCount.value--
  if (loadingCount.value <= 0) {
    Loading(false)
  }
}
const loadingFalse = () => {
  loadingCount.value = 0
  Loading(false)
}

const handleLocalBottom = (val: number) => {
  localStorage.setItem('BottomCount', val.toString())
}
export async function useHttp<T, ResT = T, DefaultT = T, PickT extends KeysOf<ResT> = KeysOf<ResT>> (
  url: string | (() => string),
  opts: UseFetchOptions<IResponse<T>, ResT, PickT, DefaultT> & CustomOptionsType = {},
) {
  // if (opts.isShowLoading) { Loading(true) }
  // const reLaunched = useCookie<boolean>('reLaunched', { default () { return false } })

  const NOT_MESSAGE_CODES = ['10071', '22031']
  const _nuxtApp = useNuxtApp()
  const { baseURL, headers, encrypt, decrypt } = useApiBaseUrl()
  let controller = null
  if (process.client) {
    controller = new AbortController()
  }
  const localId = useCookie<string>('LT')

  // const learnStore = useLearnStore()
  const {
    setLearnTargetId,
  } = useLearnTarget()
  const {
    public: { TOKEN_KEY },
  } = useRuntimeConfig()
  const userStore = useUserStore()
  const route = useRoute()
  // web-view容器（库课云校园、天一云校园小程序传递到商品详情页）
  const isMiniProgramWebView = route.query?.isMiniProgramWebView === 'true'
  const h5IframeType = route.query?.h5IframeType === '1'
  // TODO state
  const {
    isXcx,
    isMobile,
    logo2,
    type,
    theme = {},
    isPc,
    NUXT_KK_STUDENT_LOCALHOST,
    NUXT_KK_STUDENT_LOCALHOST_TY
  } = useAppConfig()
  const themeColor = encodeURIComponent(theme?.brand?.color || '')
  let isLoginMiniProgram = null
  if (isPc) {
    isLoginMiniProgram = useCookie<boolean>('isLoginMiniProgram')
  } else {
    isLoginMiniProgram = useCookie<boolean>('isLoginMiniProgram', { default () { return false } })
  }
  /**
   * 和产品、后端约定，暂时写死
   */
  const TOKEN = useCookie(TOKEN_KEY, {
    expires: new Date(Date.now() + 60 * 60 * 24 * 10 * 1000)
  })
  // const APPID_COOKIE = useCookie('APPID')
  // const LESSEE_ID_COOKIE = useCookie('LESSEEID')
  // const { href } = useRequestURL()
  const defaults = {
    // retry: 2,
    // TODO 去除正则判断
    baseURL: /^(\/api\/|http)/.test(url) ? undefined : unref(baseURL),
    key: url,
    method: 'POST',
    responseType: 'json',
    timeout: (6 + 14) * 1000,
    // body: {},
    // https://github.com/unjs/ofetch#%EF%B8%8F-interceptors
    onRequest (ctx) {
      if (process.client && opts.isShowLoading) {
        addLoading(opts.isShowMask)
        handleLocalBottom(loadingCount.value)
      }

      const { options } = ctx

      options.headers = {
        ...headers,
        ...(options?.headers || {}),
      }

      if (route.query?.kkToken) {
        // 处理 iframe 嵌套项目 路由获取 token 信息
        options.headers['kk-token'] = route.query.kkToken
      } else if (TOKEN.value) {
        options.headers['kk-token'] = TOKEN.value
      }
      const _headers = opts?.headers as Record<string, string>
      if (_headers?.['kk-token']) {
        options.headers['kk-token'] = _headers['kk-token']
      }

      // APPID_COOKIE.value && (options.headers['kk-platform'] = APPID_COOKIE.value)
      // LESSEE_ID_COOKIE.value && (options.headers['tenant-id'] = LESSEE_ID_COOKIE.value)
      if (options.headers['x-forwarded-for']) {
        options.headers['kk-ip'] = options.headers['x-forwarded-for']
      }
      // 处理分享
      if (route.query['kk-system'] === '2' && ['/kukemarketing/wap/points/pointsDiscountProt', '/kukeonlineorder/wap/order/addOrderProt'].includes(options.key)) {
        options.headers['kk-system'] = route.query['kk-system']
        options.headers['kk-Pool-Id'] = route.query.poolId
      }

      if (options.method?.toLocaleLowerCase() === 'post' &&
        !url.startsWith('/api/')
      ) {
        if (opts.encrypt) { options.headers['kk-encrypt'] = 1 }
        options.body = encrypt({ data: options.body, token: options.headers['kk-token'], type: opts.encrypt })
      }

      // loggerError(ctx, 'onRequest')
    },
    onRequestError (ctx) {
      if (process.client && opts.isShowLoading) {
        loadingFalse()
        handleLocalBottom(loadingCount.value)
      }

      // loggerError(ctx, 'onRequestError')
      console.error(ctx, 'onRequestError')
      const { aborted, reason = {} } = ctx.options?.signal || {}
      if (aborted && typeof reason === 'string') {
        console.log('onRequestError', reason)
        // 直接在组件层面处理错误 展示超时的异常信息
        showError({
          statusCode: 408,
          statusMessage: '请求超时',
          message: '服务器响应超时，请稍后重试'
        })
      }
    },
    async onResponse (ctx) {
      if (process.client && opts.isShowLoading) {
        closeLoading()
        handleLocalBottom(loadingCount.value)
      }
      const { response } = ctx
      response._data = decrypt(response._data, opts.encrypt)

      // 刷新token 两个token同时生效
      const newToken = response.headers.get('kk-token')
      if (newToken && newToken !== TOKEN.value) {
        TOKEN.value = newToken
      }
      const data = response._data
      if (data && data.code) {
        if (data.code !== '10000') {
          loggerError(ctx, 'onResponse error')

          let isShowMsg: Boolean = true

          if ([NetCode.requireReLogin, NetCode.requireReLogin].includes(data.code)) {
            // console.warn(data.msg || '登录已失效，请重新登录')

            userStore.clearUserInfo()
            // TODO
            const path = isMobile ? '/login' : '/'
            if (path === '/login' && isXcx) {
              if (!isLoginMiniProgram.value) {
                isLoginMiniProgram.value = true
                wx.miniProgram.navigateBack({
                  delta: 1,
                })
                wx.miniProgram.navigateTo({
                  url: `/pages/login/index?redirect_uri=${encodeURIComponent(
                    route.fullPath
                  )}&logo2=${logo2 || ''}&themeColor=${themeColor}`,
                  complete: function () {
                    setTimeout(() => {
                      isLoginMiniProgram.value = false
                    }, 1000)
                  },
                })
              }
            } else if (os?.isApp && os?.isAndroid) {
              // app内失效token，跳转登录页面，不需要再弹msg
              isShowMsg = false
              window.android.login()
            } else if (os?.isApp && os?.isHarmonyOS) {
              // app内失效token，跳转登录页面，不需要再弹msg
              isShowMsg = false
              window.harmony?.login()
            } else if (os?.isApp && (os?.isPhone || os?.isTablet)) {
              // app内失效token，跳转登录页面，不需要再弹msg
              isShowMsg = false
              window.webkit.messageHandlers.login.postMessage(null)
            } else if (path === '/login' && isIframe()) {
              // 如果是移动端并且的 iframe 加载
              const n = h5IframeType ? NUXT_KK_STUDENT_LOCALHOST : NUXT_KK_STUDENT_LOCALHOST_TY
              window.parent.postMessage({ type: 'login' }, n as string)
            } else if (path === '/login' && isMiniProgramWebView) {
              // 如果是小程序端并且isMiniProgramWebView为true
              window.wx.miniProgram.postMessage({ data: { type: 'login' } })
              wx.miniProgram.navigateBack()
            } else {
              // TODO 需联调
              await navigateTo({
                path,
                query: {
                  // TODO M端是否拼接？
                  redirect_uri: encodeURIComponent(route.fullPath)
                }
              })
            }
          } else if ([NetCode.findError, NetCode.offShelf].includes(data.code)) {
            // 处理错误商品id 或者已经下架或已失效商品正常进入
            throw createError({
              statusCode: data.code,
              message: data.msg,
              data,
              // fatal: false
            })
          } else if (data.code === '16501') {
            if (isXcx) {
              setLearnTargetId('idOfCurrentUser', null)
              localId.value = null
              // learnStore.setLearnDialogState(true)

              const timer = setTimeout(() => {
                wx.miniProgram.reLaunch({
                  url: '/pages/index/index',
                })
                clearTimeout(timer)
              }, 2000)

              // const key = new Date().getTime()
              // useLearnStore().setRefreshKey(key)
            } else if (type === 1) {
              setLearnTargetId('idOfCurrentUser', null)
              localId.value = null
              if (isPc) {
                await _nuxtApp.runWithContext(() => {
                  navigateTo('/', { external: true })
                })
              } else {
                await _nuxtApp.runWithContext(() => {
                  if (route.path !== '/') {
                    navigateTo('/', { external: true })
                  }
                })
                // console.log('useHttp执行了setLearnDialogState')
                // learnStore.setLearnDialogState(true)
              }
            }
          }

          if (url === '/kukemarketing/wap/pageMaster/getAssemblyForWx') { // 获取组件信息
            if (opts.body.from && opts.body.from === 'seckillList') { // 秒杀列表不需要弹窗msg
              isShowMsg = false
            }
          }
          if (isShowMsg) {
            if (!NOT_MESSAGE_CODES.includes(data.code)) {
              Message(data.msg || data.message)
            }
          }
          throw createError({
            statusCode: data.code,
            message: data.msg,
            data,
            // fatal: false
          })
        } else {
          loggerError(ctx, 'onResponse success')
          // response._data = data.data
        }
      } else {
        loggerError(ctx, 'onResponse error')
        // try {
        //   response._data = await ctx.response.json()
        // } catch (error) {
        throw new Error('_data not found')
        // }
      }
    },

    onResponseError (ctx) {
      if (process.client && opts.isShowLoading) {
        loadingFalse()
        handleLocalBottom(loadingCount.value)
      }
      loggerError(ctx, 'onResponseError')
      // TODO 确定 处理方式
      const { response } = ctx
      response._data = decrypt(response._data, opts.encrypt)
      let message = response.statusText
      if (!message) {
        message = (response._data || {}).message
      }

      switch (response.status) {
        case 400:
          // console.warn('参数错误')
          break
        case 401:
          // console.warn('没有访问权限')
          // TODO
          // router.push(`/?callback=${route.path}`)
          if (isXcx) {
            wx.miniProgram.navigateBack({
              delta: 1,
            })
          } else {
            navigateTo('/')
          }
          break
        case 403:
          // console.warn('服务器拒绝访问')
          break
        case 404:
          // console.warn('请求地址错误')
          break
        case 500:
          // console.warn('服务器故障')
          break
        default:
          // console.warn('网络连接故障')
          break
      }
      // throw new myBusinessError()
      // throw createError({ statusCode: response.status, statusMessage: message })
    },
  } as UseFetchOptions<IResponse<T>, ResT, PickT, DefaultT>
  if (process.client) {
    defaults.signal = controller?.signal
  }
  const params = defu(opts, defaults)
  let timer = null
  if (process.client) {
    timer = setTimeout(() => {
      const MESSAGE_TIMEOUT = '网络开小差了，请刷新页面~'
      controller?.abort(MESSAGE_TIMEOUT)
      timer && clearTimeout(timer)
    }, params.timeout)
  }
  // return useLazyFetch(url, params)
  const response = await useFetch(url, params)
  if (process.client) {
    timer && clearTimeout(timer)
  }
  return response
}

export function loggerError ({ request, response, options, error }: FetchContext, name: string) {
  if (process.server) {
    const _headers = {}
    const { headers = {} } = options || {}
    const code = response?._data?.code
    if (code !== '10000') {
      const _console = console
      // _console.log(`<-------- code:${code} ${new Date().toISOString()} ${name} ${request}`)
      headers?.forEach((value, key) => {
        _headers[key] = value
      })
      _console.log(JSON.stringify({
        name,
        code,
        request,
        reqTime: new Date().toISOString(),
        _headers,
        options,
        error,
        response
      }))
      // _console.log('-------->')
    }
  }
}
