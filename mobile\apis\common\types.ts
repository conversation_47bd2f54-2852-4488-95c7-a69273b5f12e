export interface SendCodeParams {
  mobile: number | string;
  scene: string;
  captchaVerifyParam: string
}
// 校验验证码
export interface CheckCodeParams extends SendCodeParams {
  code: string;
}

export interface ShareInfoDTO {
  productSide: number // 产品端 (1 pc 5小程序  7wap  9app)
  id?: string // 自定义页面ID
}

/**
 * 商品信息数据传输对象
 */
export interface GoodsInfoDTO {
  cateIds?: string; // 专业分类ID (非必须)
  cateIdNames?: string; // 专业分类名称 (非必须)
  categoryLabels?: string; // 标签 (非必须)
  goodsMasterId?: string; // 商品ID (非必须)
  goodsTitle?: string; // 商品标题 (非必须)
  goodsPresentPrice?: string; // 商品现价 (非必须)
}

/**
 * 订单行为记录数据传输对象
 */
export interface OrdersBehaviorRecordDTO {
  orderSn: string; // 订单编号 (非必须)
  receivablePrice: string; // 订单应收金额 (非必须)
  orderStatus: number; // 订单状态 (非必须)
  goodsInfos: GoodsInfoDTO[]; // 商品信息 (必须)
}

/**
 * 用于获取添加订单行为记录的参数接口
 */
export interface FetchAddOrdersBehaviorRecordParams {
  userMobile: string; // 用户手机号 (必须)
  clientType?: string; // 客户端类型 (非必须)
  behaviorClassificationUuid: string; // 行为分类UUID (必须)
  isPromote: number; // 是否推广 (必须) 0否1是
  behaviorSource: string; // 行为来源 (必须)
  records: OrdersBehaviorRecordDTO[]; // 记录数组 (必须)
}
