// 8-20位字母、数字或字符，至少包含两种
export const passwordReg = /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$).{8,20}$/

// 将手机号码中间四位替换为星号
export const maskedMobileNumber = (mobile: string | undefined) => {
  if (!mobile) {
    return ''
  }
  return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1 **** $2')
}

// 昵称输入校验-可以匹配2-10个字的中/英文、数字，不包含特殊符号或标点符号
const regex = /^[a-zA-Z0-9\u4E00-\u9FA5]{2,10}$/
export const validateNickname = (value: any, str: any) => {
  if (!value) {
    Message('请输入' + str)
    return false
  } else if (!regex.test(value)) {
    Message('昵称须在2-10个字符内，且仅支持中英文数字')
    return false
  } else {
    return true
  }
}

export const regPhone = /^1[3-9][0-9]{9}$/

/**
 * @description 金额输入
 * @params {ruleform:Object}
 * @params {key:string} 绑定哪个字段
 * @return undefined
 *  */
export const moneyInput = (e:Event, ruleform:Object, key:string) => {
  //
  // eslint-disable-next-line no-useless-escape
  ruleform[key] = (e.target as HTMLInputElement).value.replace(/[^\d^\.]+/g, '')
    .replace(/^0+(\d)/, '$1')
    .replace(/^\./, '0.')
    .match(/^\d*(\.?\d{0,2})/g)?.[0] || ''
}
