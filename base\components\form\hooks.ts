// import mitt from 'mitt'

// export interface ruleProp {
//     type: 'required' | 'email' | 'phone' | 'validate',
//     message: string,
//     validateFun?:(val:any)=>boolean
// }
// export type rule = ruleProp[];

// export type Fun = () => Boolean;
// type Events = {
//     listenSubmit: Fun;
//     bar?: number;
// };

// export const ruleReg = {
//   email: /^([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/,
//   phone: /^1[3-9][0-9]{9}$/
// }
// export const emitter = mitt<Events>()

import { events } from '@kukefe/kkutils'
export interface ruleProp {
    type: 'required' | 'email' | 'phone' | 'validate',
    message: string,
    validateFun?:(val:any)=>boolean
}
export type rule = ruleProp[];

export type Fun = () => Boolean;

export const ruleReg = {
  email: /^([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/,
  phone: /^1[3-9][0-9]{9}$/
}
export const emitter = events
