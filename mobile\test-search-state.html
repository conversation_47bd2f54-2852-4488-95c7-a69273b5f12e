<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索状态保持测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>搜索状态保持功能测试</h1>
    
    <div class="test-section">
        <h3>1. 状态保存测试</h3>
        <input type="text" id="testKeyword" placeholder="输入搜索关键字" value="测试商品">
        <button onclick="saveTestState()">保存搜索状态</button>
        <div id="saveResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h3>2. 状态恢复测试</h3>
        <button onclick="restoreTestState()">恢复搜索状态</button>
        <div id="restoreResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h3>3. 状态清理测试</h3>
        <button onclick="clearTestState()">清理搜索状态</button>
        <div id="clearResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h3>4. 过期状态测试</h3>
        <button onclick="saveExpiredState()">保存过期状态</button>
        <button onclick="testExpiredState()">测试过期状态</button>
        <div id="expiredResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h3>5. 当前存储状态</h3>
        <button onclick="showCurrentState()">显示当前状态</button>
        <div id="currentState" class="test-result"></div>
    </div>

    <script>
        const SEARCH_STATE_KEY = 'exclusive_points_goods_search_state';

        // 保存测试状态
        function saveTestState() {
            const keyword = document.getElementById('testKeyword').value;
            const state = {
                keyword: keyword,
                list: [
                    { goodsId: '1', title: '测试商品1', exchangePrice: 100 },
                    { goodsId: '2', title: '测试商品2', exchangePrice: 200 }
                ],
                isLoaded: true,
                timestamp: Date.now()
            };
            
            try {
                sessionStorage.setItem(SEARCH_STATE_KEY, JSON.stringify(state));
                showResult('saveResult', '状态保存成功！', 'success');
            } catch (error) {
                showResult('saveResult', '状态保存失败：' + error.message, 'error');
            }
        }

        // 恢复测试状态
        function restoreTestState() {
            try {
                const savedState = sessionStorage.getItem(SEARCH_STATE_KEY);
                if (savedState) {
                    const state = JSON.parse(savedState);
                    const isExpired = Date.now() - state.timestamp > 30 * 60 * 1000;
                    
                    if (!isExpired) {
                        document.getElementById('testKeyword').value = state.keyword;
                        showResult('restoreResult', 
                            `状态恢复成功！关键字：${state.keyword}，商品数量：${state.list.length}`, 
                            'success');
                    } else {
                        sessionStorage.removeItem(SEARCH_STATE_KEY);
                        showResult('restoreResult', '状态已过期，已自动清理', 'error');
                    }
                } else {
                    showResult('restoreResult', '没有找到保存的状态', 'error');
                }
            } catch (error) {
                showResult('restoreResult', '状态恢复失败：' + error.message, 'error');
            }
        }

        // 清理测试状态
        function clearTestState() {
            try {
                sessionStorage.removeItem(SEARCH_STATE_KEY);
                document.getElementById('testKeyword').value = '';
                showResult('clearResult', '状态清理成功！', 'success');
            } catch (error) {
                showResult('clearResult', '状态清理失败：' + error.message, 'error');
            }
        }

        // 保存过期状态
        function saveExpiredState() {
            const state = {
                keyword: '过期测试',
                list: [],
                isLoaded: true,
                timestamp: Date.now() - 31 * 60 * 1000 // 31分钟前
            };
            
            try {
                sessionStorage.setItem(SEARCH_STATE_KEY, JSON.stringify(state));
                showResult('expiredResult', '过期状态保存成功！', 'success');
            } catch (error) {
                showResult('expiredResult', '过期状态保存失败：' + error.message, 'error');
            }
        }

        // 测试过期状态
        function testExpiredState() {
            try {
                const savedState = sessionStorage.getItem(SEARCH_STATE_KEY);
                if (savedState) {
                    const state = JSON.parse(savedState);
                    const isExpired = Date.now() - state.timestamp > 30 * 60 * 1000;
                    
                    if (isExpired) {
                        sessionStorage.removeItem(SEARCH_STATE_KEY);
                        showResult('expiredResult', '过期状态检测正常，已自动清理', 'success');
                    } else {
                        showResult('expiredResult', '状态未过期', 'error');
                    }
                } else {
                    showResult('expiredResult', '没有找到状态数据', 'error');
                }
            } catch (error) {
                showResult('expiredResult', '过期状态测试失败：' + error.message, 'error');
            }
        }

        // 显示当前状态
        function showCurrentState() {
            try {
                const savedState = sessionStorage.getItem(SEARCH_STATE_KEY);
                if (savedState) {
                    const state = JSON.parse(savedState);
                    const ageMinutes = Math.floor((Date.now() - state.timestamp) / (1000 * 60));
                    showResult('currentState', 
                        `当前状态：关键字="${state.keyword}"，商品数量=${state.list.length}，已保存${ageMinutes}分钟`, 
                        'success');
                } else {
                    showResult('currentState', '当前没有保存的状态', 'error');
                }
            } catch (error) {
                showResult('currentState', '获取当前状态失败：' + error.message, 'error');
            }
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }

        // 页面加载时显示当前状态
        window.onload = function() {
            showCurrentState();
        };
    </script>
</body>
</html>
