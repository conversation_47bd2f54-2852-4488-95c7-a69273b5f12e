/**
 * 兑换中心
 * -
 * 激活码兑换
 */
export async function activationCodeExchange (body: {activationCode:string}) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/activationCodeExchange', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
   * 兑换中心
   * -
   * 地址填写
   */
export async function userClaimInformation (body:any) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/userClaimInformation', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
   * 兑换中心
   * -
   * 兑换记录
   */
export async function exchangeRecord (body:any) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/exchangeRecord', {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}
