/**
 * 判断crm试听剩余有效期
 * @useAuditionValidity
 */
const useAuditionValidity = () => {
  const id = useRoute().params.id as string
  const { path } = useRoute()
  const paramsId = useCourseId(id, path, 'course')
  const kkInternalSystem = useCookie<string>(INNERNAL_SYSTEM)
  const kkIsAudition = useCookie<string>(IS_AUDITION)
  const kkPoolId = useCookie<string>(POOL_ID)
  const auditionValidity = useCookie<string>(AUDITION_VALIDITY)

  const getAuditonValidity = async () => {
    try {
      // 判断crm试听剩余有效期
      if (Number(kkInternalSystem.value) === 26 && Number(kkIsAudition.value) === 1 && auditionValidity.value && Number(auditionValidity.value) > 0) {
        const { data } = await useHttp('/kukecoregoods/wap/goods/getCrmTryListenLeftValiditySecond', { method: 'post', body: { id: paramsId, poolId: kkPoolId.value } })
        const leftHour = data?.value?.data?.tryListenLeftValiditySecond
        if (!leftHour || Number(leftHour) <= 0) {
          Message('已过试听有效期，请购买后立即观看', '', 1000)
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    } catch {
      return true
    }
  }

  return {
    getAuditonValidity
  }
}

export default useAuditionValidity
