import CryptoJS from 'crypto-js'
import forge from 'node-forge'
// 加密私钥
const privateKeyPem = `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// rsa 解密 非对称
const decryptData = (data:any) => {
  const encryptedData = data
  const privateKey = forge.pki.privateKeyFromPem(privateKeyPem)
  const encryptedBytes = forge.util.decode64(encryptedData)

  const decryptedBytes = privateKey.decrypt(encryptedBytes, 'RSA-OAEP', {
    md: forge.md.sha256.create(),
    mgf1: {
      md: forge.md.sha1.create()
    }
  })
  const decryptedData = forge.util.decodeUtf8(decryptedBytes)
  return decryptedData
}

// aes对称解密 CryptoJS.enc.Base64.parse CryptoJS.enc.Utf8 必须得有
const createDecipher = (encrypted:string, key:string, iv:string) => {
  return CryptoJS.AES.decrypt({
    ciphertext: CryptoJS.enc.Base64.parse(encrypted)
  },
  CryptoJS.enc.Utf8.parse(key),
  {
    iv: CryptoJS.enc.Utf8.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  }).toString(CryptoJS.enc.Utf8)
}

export {
  decryptData,
  createDecipher
}
