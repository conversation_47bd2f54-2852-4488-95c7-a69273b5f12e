import type {
  MySurveysListRes,
  SurveysDetailRes
} from './types'
export enum CourseEvaluateApi {
  mySurveysListProt = '/kukeeducenter/wap/keSurveys/mySurveysListProt',
  surveysDetailProt = '/kukeeducenter/wap/keSurveys/surveysDetailProt',
  surveysSubmitDetailProt = '/kukeeducenter/wap/keSurveys/surveysSubmitDetailProt',
  submitSurveysProt = '/kukeeducenter/wap/keSurveys/submitSurveysProt',
}
// 学员我的课评列表
export async function getMySurveysList (body: any) {
  return useHttp<{ data: MySurveysListRes }>(CourseEvaluateApi.mySurveysListProt, {
    method: 'post',
    isShowLoading: true,
    body
  })
}

// 课评详情
export async function getSurveysDetail (body: any) {
  return useHttp<{ data: SurveysDetailRes }>(CourseEvaluateApi.surveysDetailProt, {
    method: 'post',
    body
  })
}

// 学员课评填写详情
export async function getSurveysSubmitDetail (body: any) {
  return useHttp<{ data: SurveysDetailRes }>(CourseEvaluateApi.surveysSubmitDetailProt, {
    method: 'post',
    body
  })
}

// 学员提交课评
export async function submitSurveys (body: any) {
  return useHttp<any>(CourseEvaluateApi.submitSurveysProt, {
    method: 'post',
    body
  })
}
