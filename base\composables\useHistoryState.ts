export function useHistoryState () {
  const route = useRoute()
  // 设置history state
  function setHistoryState (state: any) {
    const url = `${route.path}?${objToUrlParams(state)}`
    const replaceState = {
      current: url,
      back: history.state ? history.state.back || '' : '',
      replaced: true
    }
    history.replaceState(replaceState, '', `?${objToUrlParams(state)}`)
    // 存储用于小程序中刷新页面
    localStorage.setItem('questionBankHistoryState', history.state.current)
  }
  // 获取history state
  function getHistoryState () {
    return history.state
  }
  // 监听history state变化
  function listenHistoryState (callback: (state: any) => void) {
    window.addEventListener('popstate', (e) => {
      callback(e.state)
    })
  }

  function transformValue<T> (value: any, transformFn: (val: any) => T): T | null {
    return value !== null ? transformFn(value) : value
  }
  /**
   * 转换对象
   *
   * @param data 要转换的数据
   * @returns 转换后的对象
   */
  function transformObject (data: any) {
    const transformMap: Record<string, (val: any) => any> = {
      moduleManageId: String,
      studyLevel1: Number,
      subjectType: Number,
      region: Number,
      directionType: Number,
      academicSection: Number,
      examFormat: Number,
      targetType: Number,
      targetId: String,
      expiry: Number,
      moduleType: Number,
      itemTitle: String
    }
    // 创建一个新的对象 output，将 data 对象的所有属性和值复制到 output 中
    const output: any = { ...data }
    // 遍历转换映射对象的所有键值对
    for (const [key, transformFn] of Object.entries(transformMap)) {
      // 如果 data 对象中存在当前字段
      if (data[key] !== undefined && data[key] !== 'undefined') {
        // 调用对应的转换函数对字段值进行转换，并将结果赋值给 output 对象的相应字段
        output[key] = transformValue(data[key], transformFn)
      }
    }
    return output
  }

  // 对象转换为url参数
  function objToUrlParams (obj: any) {
    let params = ''
    for (const key in obj) {
      params += `${key}=${obj[key]}&`
    }
    return params.substring(0, params.length - 1)
  }

  return {
    setHistoryState,
    getHistoryState,
    listenHistoryState,
    transformObject
  }
}
