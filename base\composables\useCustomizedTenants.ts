// 该方法不再维护，谨慎使用
export default function (props: any, byLesseeId = false) {
  const { appId, lesseeId } = useAppConfig()
  const { public: { BUILD_ENV } } = useRuntimeConfig()
  // console.log(`当前租户: ${lesseeId}; 当前产品线: ${appId}`)
  const isApplyProducts = computed(() => {
    const ids = props?.tenants.map((key: string) => {
      if (!CUSTOMIZED_TENANTS[key]) {
        console.log(`[ CUSTOMIZED_TENANTS.${key} ] required`)
      } else if (!CUSTOMIZED_TENANTS[key][BUILD_ENV]) {
        console.log(`[ CUSTOMIZED_TENANTS.${key}.${BUILD_ENV} ] required`)
      }
      const { LESSEE_ID, APP_ID } = (CUSTOMIZED_TENANTS[key] || {})[BUILD_ENV] || {}
      return [LESSEE_ID, APP_ID]
    })

    let r = false
    if (!byLesseeId) { r = ids?.some(([a, b],) => (a === lesseeId && b === appId)) } else {
      r = ids?.some(([a,],) => (a === lesseeId))
    }
    return r
  })
  return { CUSTOMIZED_TENANTS, isApplyProducts }
}
export const CUSTOMIZED_TENANTS: {
  [key: string]: {
    [key: string]: {
      LESSEE_ID: string
      APP_ID: string | number
    }
  }
} = {
  kuke99_com: {
    // 生产环境必须配置
    // 迁移前对于域名：https://kkceshi.kukecloud.cn
    PROD: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    LOCAL: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    LOCAL_STAGING: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    DEV: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    STAGING: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    LOCAL_PRESSURE: {
      LESSEE_ID: '',
      APP_ID: 1001,
    },
    PRESSURE: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    LOCAL_PRE: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    PRE: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
  },
  kuke99_com1: {
    // 生产环境必须配置
    // 迁移前对于域名：https://kkceshi.kukecloud.cn
    PROD: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    LOCAL: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    LOCAL_STAGING: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    DEV: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    STAGING: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    LOCAL_PRESSURE: {
      LESSEE_ID: 'kk2024000018',
      APP_ID: 1000,
    },
    PRESSURE: {
      LESSEE_ID: 'kk2024000018',
      APP_ID: 1,
    },
    PRE: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
  },
  // 广东
  gdkuke99_com: {
    // 必须配置 生产
    PROD: {
      LESSEE_ID: '',
      APP_ID: 8,
    },
    // 本地dev
    LOCAL: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    // 本地 test
    LOCAL_STAGING: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    // dev
    DEV: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    // test
    STAGING: {
      LESSEE_ID: '',
      APP_ID: 15137,
    },
    // 压测
    LOCAL_PRESSURE: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    PRESSURE: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
    // 预发布
    PRE: {
      LESSEE_ID: '',
      APP_ID: 1,
    },
  },
  // 公职系统
  tyedu99_com: {
    // 必须配置 生产
    PROD: {
      LESSEE_ID: 'kk2024000011',
      APP_ID: 1001,
    },
    // 本地dev
    LOCAL: {
      LESSEE_ID: 'aa',
      APP_ID: 1,
    },
    // 本地 test
    LOCAL_STAGING: {
      LESSEE_ID: 'aa',
      APP_ID: 1,
    },
    // dev
    DEV: {
      LESSEE_ID: 'aa',
      APP_ID: 1,
    },
    // test
    STAGING: {
      LESSEE_ID: 'aa',
      APP_ID: 1,
    },
    // 压测
    LOCAL_PRESSURE: {
      LESSEE_ID: 'kk2024000018',
      APP_ID: 1,
    },
    PRESSURE: {
      LESSEE_ID: 'aa',
      APP_ID: 1,
    },
    LOCAL_PRE: {
      LESSEE_ID: 'kk2024000092',
      APP_ID: 1001,
    },
    // 预发布
    PRE: {
      LESSEE_ID: 'kk2024000092',
      APP_ID: 1001,
    },
  },
  // 河北
  // 四川
  // ...
}
