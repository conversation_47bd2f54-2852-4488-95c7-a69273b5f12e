let isMathjaxConfig: boolean = false // 用于标识是否配置

/**
 * [AI-GEN] 添加等待 MathJax 加载完成的函数
 */
const waitForMathJax = (): Promise<void> => {
  return new Promise((resolve) => {
    if (window?.MathJax?.tex2chtml) {
      resolve()
      return
    }

    // 检查间隔为 100ms，最多等待 5 秒
    const maxAttempts = 50
    let attempts = 0

    const checkMathJax = () => {
      if (window?.MathJax?.tex2chtml) {
        resolve()
        return
      }

      attempts++
      if (attempts < maxAttempts) {
        setTimeout(checkMathJax, 100)
      } else {
        console.warn('MathJax load timeout')
        resolve() // 即使超时也resolve，让程序继续运行
      }
    }

    checkMathJax()
  })
}

const initMathjaxConfig = (): void => {
  if (!window.MathJax) {
    return
  }
  window.MathJax.Hub.Config({
    showProcessingMessages: false, // 关闭js加载过程信息
    messageStyle: 'none', // 不显示信息
    extensions: ['tex2jax.js'],
    jax: ['input/TeX', 'output/HTML-CSS'],
    tex2jax: {
      inlineMath: [
        ['$', '$'],
        ['\\(', '\\)']
      ], // 行内公式选择符
      displayMath: [
        ['$$', '$$'],
        ['\\[', '\\]']
      ], // 段内公式选择符
      skipTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code', 'a'], // 避开某些标签
      processEscapes: true,
      processEnvironments: true
    },
    'HTML-CSS': {
      availableFonts: ['STIX', 'TeX'], // 可选字体
      showMathMenu: false, // 关闭右击菜单显示
    }
  })

  isMathjaxConfig = true // 配置完成，改为true
}

const MathQueue = (element?: HTMLElement | null): void => {
  if (!window.MathJax) { return }
  if (element) {
    // 指定某个元素id渲染
    window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub, element])
  } else {
    // 整个DOM渲染
    window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub])
  }
}

/**
 * LaTeX 公式匹配的正则表达式
 * 正则表达式以匹配以下格式的 LaTeX 公式：
  $$...$$（行间公式）
  \[...\]（行间公式）
  \(...\)（行内公式）
  $...$（行内公式）
  使用了正则表达式的 g 和 s 标志：
  g：全局匹配
  s：允许.匹配换行符
  使用 | 运算符组合多个匹配模式
 */
const LATEX_REGEX = /(\$\$.*?\$\$|\\\[.*?\\\]|\\\(.*?\\\)|\$.*?\$)/s

const filterLatex = (input: string): string => {
  // 使用 replace 方法将匹配到的 LaTeX 公式替换为空字符串
  return input.replace(LATEX_REGEX, '')
}

/**
 * 判断给定的字符串中是否包含 LaTeX 公式
 * @param input - 输入的字符串
 * @returns 如果包含 LaTeX 公式则返回 true，否则返回 false
 */
const containsLatex = (input: string): boolean => {
  // 使用 test 方法检查字符串中是否匹配 LaTeX 公式
  return LATEX_REGEX.test(input)
}

export default {
  waitForMathJax,
  isMathjaxConfig,
  initMathjaxConfig,
  MathQueue,
  filterLatex,
  containsLatex
}
