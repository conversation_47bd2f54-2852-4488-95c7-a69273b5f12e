import Clipboard from 'clipboard'
// 复制文本
// TODO 内存泄漏可疑点: ？？？
export function handleClipboard (...rest: any) {
  const [text, event, successMsg = '复制成功~', errorMsg = '复制失败!', callback] = rest
  const clipboard = new Clipboard(event.target, {
    text: () => text
  })

  clipboard.on('success', () => {
    Message(successMsg)
    clipboard.destroy()
    callback && callback()
  })

  clipboard.on('error', () => {
    Message(errorMsg)
    clipboard.destroy()
  })

  clipboard.onClick(event)
}
