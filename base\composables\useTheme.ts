import { generate } from '@ant-design/colors'
import { hexToRgb } from '../utils/index'

const themeColorVars = ['light5', 'light4', 'light3', 'light2', 'light1', '', 'dark1', 'dark2', 'dark3', 'dark4']

/**
 * TODO 结合 vue 的 reactive
 */
export default () => {
  //
  const { theme } = useAppConfig()
  const { brand = { }, marketing = {}, PREFIX_NAME } = theme || {}
  const vars = {}
  const { r, g, b } = hexToRgb(brand.color || '#eb2330')
  vars['brand-rgb'] = `${r} ${g} ${b}`
  //
  const colors = generate(brand.color || '#eb2330')
  themeColorVars.forEach((key, i) => {
    vars[`brand${key ? '-' : ''}${key}`] = colors[i]
  })
  //
  vars['marketing--deg'] = marketing.deg
  vars['marketing--1'] = marketing.color1
  if (marketing.color2) {
    vars['marketing--2'] = marketing.color2
  }
  const { r: r1, g: g1, b: b1 } = hexToRgb(marketing.color1)
  vars['marketing-rgb'] = `${r1} ${g1} ${b1}`
  //
  if (marketing.custom && !marketing.color2) {
    vars.marketing = `var(${PREFIX_NAME}marketing--1)`
  } else {
    vars.marketing = `linear-gradient(var(${PREFIX_NAME}marketing--deg), var(${PREFIX_NAME}marketing--1) 0%, var(${PREFIX_NAME}marketing--2, var(${PREFIX_NAME}marketing--1)) 100%)`
  }
  let result = ''
  for (const key in vars) {
    if (Object.prototype.hasOwnProperty.call(vars, key)) {
      const element = vars[key]
      result += `${PREFIX_NAME}${key}: ${element};`
    }
  }
  const themeContent = computed(() => {
    return `:root { ${result} }`
  })
  useHead({
    style: {
      id: 'currentTheme',
      innerHTML: themeContent.value
      // return result.replace(/\s{2}/g, '')
    },
    // htmlAttrs: {
    //   'data-theme': theme.currentTheme
    // },
  })
  return { theme, vars }
}
//
export const formatTheme = (data) => {
  const {
    marketingDirection = 'horizontal',
    wholeStationEssentialColour,
    wholeStationColour,
    wholeStationColourCustom,
    marketingMatchColors,
    marketingColors,
    marketingGradientRamp,
    marketingColorsCustom,
    marketingGradientRampCustom,
  } = data || {}
  const gradualDirectionMapping = {
    crossFromZero: '45deg',
    horizontal: '90deg',
    crossFromBottom: '135deg',
    vertical: '180deg'
  }
  const DEFAULT_BRAND_COLOR = '#eb2330'
  const result = {
    PREFIX_NAME: '--kkc-',
    default: DEFAULT_BRAND_COLOR,
    brand: {
      color: DEFAULT_BRAND_COLOR,
    },
    marketing: {
      color1: DEFAULT_BRAND_COLOR,
      deg: gradualDirectionMapping[marketingDirection] || '90deg',
    }
  }
  if (wholeStationEssentialColour === 1 && wholeStationColour) {
    result.brand.color = wholeStationColour
  } else if (wholeStationEssentialColour === 2 && wholeStationColourCustom) {
    result.brand.color = wholeStationColourCustom
    result.brand.custom = true
  }
  // const { r, g, b } = hexToRgb(result.brand.color)
  // result.brand.r = r
  // result.brand.g = g
  // result.brand.b = b
  // result.brand.colors = generate(result.brand.color)

  if (marketingMatchColors === 1 && marketingColors) {
    result.marketing.color1 = marketingColors
    result.marketing.color2 = marketingGradientRamp
  } else if (marketingMatchColors === 2 && marketingColorsCustom) {
    result.marketing.custom = true
    result.marketing.color1 = marketingColorsCustom
    result.marketing.color2 = marketingGradientRampCustom
    // TODO 生成 --kkc-marketing--[hover|active|]disabled 色值
  }
  return result
}
