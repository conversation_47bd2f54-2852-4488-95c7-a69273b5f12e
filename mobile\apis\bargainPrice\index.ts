import type { BargainInfo, RecordList, VerifyActivity } from './types'

enum bargainPrice {
    bargainActivityList = '/kukecoupon/wap/marketingGoods/bargainActivityList',
    verifyBargainActivity ='/kukecoupon/wap/marketingGoods/verifyBargainActivity',
    bargainRecord ='/kukecoupon/wap/marketingGoods/bargainRecord',
    oneselfBargain ='/kukecoupon/wap/marketingGoods/oneselfBargain',
    bargainShare ='/kukecoupon/wap/marketingGoods/bargainShare',
    friendHelpBargain ='/kukecoupon/wap/marketingGoods/friendHelpBargain',
    bargainShareFriend ='/kukecoupon/wap/marketingGoods/bargainShareFriend',
}

// 砍价活动列表
export async function bargainActivityList (body: any) {
  return useHttp<BargainInfo>(bargainPrice.bargainActivityList, {
    method: 'post',
    body,
    transform: res => res?.data,
  })
}

// 砍价活动校验
export async function verifyBargainActivity (body: VerifyActivity) {
  return useHttp<void>(bargainPrice.verifyBargainActivity, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 砍价记录
export async function bargainRecord (body: any) {
  return useHttp<RecordList>(bargainPrice.bargainRecord, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 用户自己砍价
export async function oneselfBargain (body: any) {
  return useHttp<any>(bargainPrice.oneselfBargain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 用户砍价分享
export async function bargainShare (body: any) {
  return useHttp<any>(bargainPrice.bargainShare, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 好友帮忙砍价
export async function friendHelpBargain (body: any) {
  return useHttp<any>(bargainPrice.friendHelpBargain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 用户砍价分享好友
export async function bargainShareFriend (body: any) {
  return useHttp<any>(bargainPrice.bargainShareFriend, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
