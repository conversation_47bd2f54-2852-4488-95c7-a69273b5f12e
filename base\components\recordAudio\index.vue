<template>
  <div class="record-audio">
    <div v-if="hasTitle" class="title">
      音频录制中
    </div>
    <div v-if="isAnswer" class="text-[#111] text-[30px] font-500">
      {{ countdown > 0 ? `倒计时${formattedCountdown}后结束` : '已录制10分钟，点击完成录制发送' }}
    </div>
    <div class="active">
      <div
        :class="['recorder-voice-btn', !isPause ? '' : 'recorder-voice-pause', isInPc ? 'pc' : 'wap']"
        @click.stop="onClickBtn"
      >
        <div
          :class="['recorder-voice-btn-absolute', !isPause ? 'recorder-voice-btn-circle' : 'recorder-voice-btn-pause']"
        />
        <div
          :class="['recorder-voice-btn-absolute', !isPause ? 'recorder-voice-btn-circle' : 'recorder-voice-btn-pause']"
        />
        <div
          :class="['recorder-voice-btn-absolute', !isPause ? 'recorder-voice-btn-circle' : 'recorder-voice-btn-pause']"
        />
      </div>
    </div>
    <div :class="['complete', isInPc ? 'pc' : 'wap']" @click="handleComplete">
      完成录制{{ isAnswer ? '' : '（限制10分钟）' }}
    </div>
  </div>
</template>
<script setup lang="ts">
const { uploadHttp } = useRecorder()
// 是否暂停
const isPause = ref(false)
const recBlob = ref(null)

const props = defineProps<{
  isOpen: boolean,
  hasTitle?: boolean,
  isInPc: boolean,
  type?: string
}>()
const emit = defineEmits<{
  (e: 'close', val: boolean): void,
  (e: 'getUrl', url: string): void,
  (e: 'getMime', flie: File): void,
  (e: 'getDuration', v:number): void,

}>()
console.log('~~~rec', window.rec)

const countdown = ref(600)
// const countdown = ref(60)
const intervalId = ref()
const pausedTime = ref(0)

const isAnswer = computed(() => props.type === 'answer')

const formattedCountdown = computed(() => {
  const minutes = Math.floor(countdown.value / 60)
  const seconds = countdown.value % 60
  return `${minutes < 10 ? '0' : ''}${minutes}m${seconds < 10 ? '0' : ''}${seconds}s`
})

// 开始录音
const recStart = () => {
  if (!window.rec) {
    console.error('未打开录音')
    Message('未打开录音')
    return
  }
  window.rec.start()
  console.log('已开始录音')
  // 录音开始 10分钟倒计时
  if (isAnswer.value) {
    startCountdown()
  }
}
// 启动倒计时
const startCountdown = () => {
  intervalId.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(intervalId.value)
      isPause.value = true
    }
  }, 1000)
}

// 暂停倒计时
const pauseCountdown = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
    intervalId.value = null
    pausedTime.value = countdown.value
  }
}

// 恢复倒计时
const resumeCountdown = () => {
  if (pausedTime.value > 0) {
    countdown.value = pausedTime.value
    startCountdown()
  }
}

// 清除倒计时
const clearCountdown = () => {
  if (isAnswer.value) {
    if (intervalId.value) {
      clearInterval(intervalId.value)
      intervalId.value = null
    }
    countdown.value = 600
    pausedTime.value = 0
  }
}

// 完成录音
const recComplete = () => {
  if (!window.rec) {
    Message('未打开录音')
    console.error('未打开录音')
    return
  }
  clearCountdown()
  window.rec.stop((blob, duration) => {
    console.log(duration, 'duration recComplete')
    // if (!duration || duration < 1000 || duration > 61000) {
    //   duration > 61000 ? Message('您的录音太长啦') : Message('您的录音太短啦')
    if (!duration || duration < 1000 || duration > 601000) {
      duration > 601000 ? Message('您的录音太长啦') : Message('您的录音太短啦')

      isPause.value = true
      window.rec.close()// 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
      emit('close', false)
      return
    }
    // blob就是我们要的录音文件对象，可以上传，或者本地播放
    recBlob.value = blob
    const localUrl = (window.URL || webkitURL).createObjectURL(blob)
    console.log('录音成功', blob, localUrl, '时长:' + duration + 'ms')
    emit('getDuration', duration)
    upload(blob)// 把blob文件上传到服务器
    isPause.value = true
    window.rec.close()// 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
    emit('close', true)
  }, (err) => {
    isPause.value = true
    Message('录音出错，未开始录音。请检查麦克风设备是否正常工作')
    console.error('结束录音出错：' + err)
    window.rec.close()// 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
    emit('close', false)
  })
}

watch(() => props.isOpen, (newVal) => {
  if (newVal) {
    console.log('isOpen: ', newVal)
    isPause.value = false
    // 开始录制
    recStart()
  } else {
    isPause.value = true
    window.rec.close()
    clearCountdown()
  }
},
{
  immediate: true,
}
)

watch(() => isPause.value, (newVal) => {
  console.log('isPause111: ', newVal)
  if (newVal) {
    window.rec.pause()
    pauseCountdown()
  } else {
    window.rec.resume()
    resumeCountdown()
  }
})

// 录音上传
const upload = async (blob: any) => {
  const file = blobToFile(blob)
  console.log('file: ', file)
  emit('getMime', file)
  const url = await uploadHttp(file, props.isInPc)
  console.log('·····url: ', url)
  emit('getUrl', url)
}

const blobToFile = (blob: any) => {
  return new File([blob], 'XXXXX.mp3', { type: blob.type })
}

const onClickBtn = () => {
  if (isAnswer.value) {
    if (countdown.value <= 0) {
      isPause.value = true
    } else {
      isPause.value = !isPause.value
    }
  } else {
    isPause.value = !isPause.value
  }
}

const handleComplete = () => {
  recComplete()
}

onUnmounted(() => {
  clearCountdown()
})

</script>

<style lang="scss" scoped>
.record-audio {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  margin: auto;

  .title {
    font-size: 20px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #111111;
    text-align: center;
  }

  .active {
    margin: 20px auto 20px;
    width: 100%;
    height: 138px;
    display: flex;
    justify-content: center;
    background-image: url('../../assets/record/record-bg.png');
    background-repeat: no-repeat;
    background-size: 100%;
  }

  .complete {
    background: var(--kkc-brand);
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFang SC;
    font-weight: 500;
    color: #F7F7F7;

    &.pc {
      margin: 4px 0 0 0;
      width: 200px;
      height: 40px;
      border-radius: 8px;
      font-size: 16px;
    }

    &.wap {
      width: 100%;
      height: 44px;
      border-radius: 8px;
      font-size: 16px;

    }
  }
}

.recorder {
  &-voice {
    display: flex;
    box-sizing: content-box;
    justify-content: center;
    align-items: center;
    padding: 48px 24px 4px;

    &-bg {
      position: absolute;
      width: 694px;
    }

    &-btn {
      position: relative;
      width: 138px;
      height: 138px;
      margin: 0 20px;
      border-radius: 50%;
      background: #fff;

      &.pc {
        &::after {
          width: 69px;
          height: 69px;
        }
      }

      &.wap {
        &::after {
          width: 69px;
          height: 69px;
        }

        div {
          @apply absolute w-[40px] h-[40px] left-[50%] top-[50%] z-[9] rounded-[50%];
          transform: translate(-50%, -50%);
        }
      }

      &::before {
        position: absolute;
        width: 69px;
        height: 69px;
        content: '';
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: var(--kkc-brand);
        border-radius: 50%;
      }

      &::after {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        content: '';
        background-image: url('../../assets/record/icon_record.png');
        background-repeat: no-repeat;
        background-size: 32px 32px;
        background-position: center;
        z-index: 10;
      }

      &-absolute {
        @apply absolute w-[40px] h-[40px] left-[50%] top-[50%] z-[9] rounded-[50%];
        transform: translate(-50%, -50%);
      }

      &-circle {
        border: 20px solid var(--kkc-brand);
        // border-radius: 50%;
        opacity: 0;
        transform: scale(0);
        animation: ripple 2s infinite;

        &:nth-child(2) {
          animation-delay: 0.7s;
        }

        &:nth-child(3) {
          animation-delay: 1.4s;
        }
      }

      &-pause {
        // border-radius: 50%;

        &:nth-child(2) {
          @apply bg-brand/20;
          width: 100px !important;
          height: 100px !important;
          z-index: 10 !important;
        }

        &:nth-child(3) {
          @apply bg-brand/10;
          width: 138px !important;
          height: 138px !important;
          z-index: 9 !important;
        }
      }
    }

    &-pause {
      // &.pc {
      //   &::after {
      //     width: 69px;
      //     height: 69px;
      //   }
      // }

      // &.wap {
      //   &::after {
      //     width: 138px;
      //     height: 138px;
      //   }

      // }

      &::after {
        position: absolute;
        content: '';
        width: 69px;
        height: 69px;
        background-image: url('../../assets/record/icon_norecord.png');
        background-repeat: no-repeat;
        background-position: center;
        background-size: 32px 32px;
        z-index: 10;
      }
    }

    &-line {
      width: 9px;
      height: 260px;
      border-radius: 9px;
      margin: 0 10px;
      z-index: 1;

      &-0 {
        height: 0;
      }
    }

    .one {
      animation: wave 0.4s 1s linear infinite alternate;
    }

    .two {
      animation: wave 0.4s 0.9s linear infinite alternate;
    }

    .three {
      animation: wave 0.4s 0.8s linear infinite alternate;
    }

    .four {
      animation: wave 0.4s 0.7s linear infinite alternate;
    }

    .five {
      animation: wave 0.4s 0.6s linear infinite alternate;
    }

    .six {
      animation: wave 0.4s 0.5s linear infinite alternate;
    }

    .seven {
      animation: wave 0.4s linear infinite alternate;
    }

    .eight {
      animation: wave 0.4s 0.3s linear infinite alternate;
    }

    .nine {
      animation: wave 0.4s 0.2s linear infinite alternate;
    }
  }

  &-btn {
    display: flex;
    height: 260px;
    justify-content: center;
    align-items: center;
  }

  &-footer-btn {
    width: 694px;
    height: 80px;
    margin: 20px auto;
    font-size: 30px;
    font-weight: 500;
    text-align: center;
    line-height: 75px;
    border-radius: 45px;
    border: 2px solid var(--kkc-brand);
    color: var(--kkc-brand);

    &-disabled {
      color: #d1cbcb;
      border-color: #d1cbcb;
    }
  }
}

@keyframes wave {
  0% {
    border-radius: 9px;
    transform: scale(1, 1);
    // background-color: royalblue;
    background-color: #f5f6f9;
  }

  100% {
    border-radius: 9px;
    transform: scale(1, 0.2);
    // background-color: indianred;
    background-color: #f5f6f9;
  }
}

@keyframes ripple {
  from {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }

  to {
    transform: translate(-50%, -50%) scale(3.5);
    opacity: 0;
  }
}
</style>
