// TODO 内存泄漏可疑点: 可变全局变量 ？？？
// 假设这是一个全局的WeakMap，用于缓存元素和其shadowRoot的关系
const shadowRootMap = new WeakMap<HTMLElement, ShadowRoot>()
const STYLE_ID = 'rich-text-shadow-style'
function toShadowDom (el: HTMLElement, htmlText: string, styleText = '') {
  // 参数验证
  if (!el || typeof el !== 'object' || !(el instanceof HTMLElement)) {
    console.error('Invalid element provided.')
    return
  }
  if (typeof htmlText !== 'string' || htmlText.trim() === '') {
    console.error('HTML text must be a non-empty string.')
    return
  }

  let shadowRoot = shadowRootMap.get(el)
  if (!shadowRoot) {
    try {
      shadowRoot = el.attachShadow({ mode: 'open' })
      shadowRootMap.set(el, shadowRoot)
    } catch (error) {
      console.error('Failed to attach shadow root:', error)
      return
    }
  }
  // TODO 清理和转义htmlText与styleText以防XSS
  // el.innerHTML = ''
  // 清空shadowRoot的内容，准备新的HTML
  shadowRoot.innerHTML = ''
  // 插入HTML
  shadowRoot.innerHTML = htmlText
  // 如果有样式文本，则插入
  if (styleText) {
    let styleElement = shadowRoot.getElementById(STYLE_ID)

    if (styleElement) {
      // 更新已有style元素的文本内容
      styleElement.textContent = styleText
    } else {
      // 创建新的style元素并指定ID
      styleElement = document.createElement('style')
      styleElement.id = STYLE_ID
      styleElement.textContent = styleText
      shadowRoot.appendChild(styleElement)
    }
  }
}

function handleBindingVal (binding: any) {
  let htmlText, styleText
  if (typeof binding.value === 'object') {
    htmlText = binding.value.htmlText
    styleText = binding.value.styleText
  } else {
    htmlText = binding.value
  }
  return { htmlText, styleText }
}

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('richText', {
    mounted (el, binding) {
      const { htmlText, styleText } = handleBindingVal(binding)
      toShadowDom(el, htmlText, styleText)
    },
    updated (el, binding) {
      const { oldValue = '', value = '' } = binding
      if (oldValue && oldValue && oldValue === value) { return false }
      const { htmlText, styleText } = handleBindingVal(binding)
      toShadowDom(el, htmlText, styleText)
    },
  })
})
