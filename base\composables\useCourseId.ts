export default function (id:string, path: string, type: string) {
  const list = ['kuke99_com']
  if (type === 'course') {
    list.push('kuke99_com1')
  }
  const { isApplyProducts } = useCustomizedTenants({ tenants: list })
  // const newsList = ['zsb', 'sydw', 'jszp', 'jszg', 'tgjs', 'gwy', 'szyf', 'xmt', 'kkgw', 'kknews', 'cjkj', 'zjkj']
  const lessonList = ['lesson']
  const dynamicPartRegex = /\/(\w+)\/[^/]+\.html/
  const match = path.match(dynamicPartRegex)
  // 提取动态部分（如 "classroom"）
  const dynamicPart = match ? match[1] : ''
  // 兼容老网校课程
  let result = id
  const flag = lessonList.includes(dynamicPart)
  switch (id) {
    case '913141':
      id = '942756'
      break
    case '913142':
      id = '960001'
      break
    case '913143':
      id = '938006'
      break
    case '913144':
      id = '942757'
      break
    case '913145':
      id = '938476'
      break
  }
  if (isApplyProducts.value) {
    switch (type) {
      // case 'course':
      //   result = dynamicPart ? `${id}_wx_${dynamicPart}` : id
      //   break
      // case 'news':
      //   result = newsList.includes(dynamicPart) ? `${id}_wx` : id
      //   break
      case 'lesson':
        result = flag ? `${id}_wx_course` : id
    }
  }

  return result
}
