// 资料列表
export interface listMaterialParams {
  cateId?: string, // 分类id
  province?: string, // 省
  city?: string, // 市
  area?: string, // 区
  directionId?: string, // 方向
  academicSectionId?: string, // 学段
  subjectId?: string, // 科目
  examFormatId?: string, // 考试形式
  syncInfo: string, // 是否同步资讯展示 首页跳转传0 资讯详情跳转传1
  type?: string, // 类型 1专升本 2公考
}

// 资讯详情-资料列表
export interface newsListMaterialParams {
  cateId?: string, // 分类id
  province: string, // 省
  city?: string, // 市
  area?: string, // 区
  directionId?: string, // 方向
  academicSectionId?: string, // 学段
  subjectId?: string, // 科目
  examFormatId?: string, // 考试形式
}
/**
 * 资料列表传参
 */
export interface listl {
  /**
   * 订单编号，必须
   */
  orderSn: string;

  /**
   * 订单id，必须
   */
  orderId: string;

  /**
   * 应收金额，必须
   */
  shouldPay: string;

  /**
   * 订单状态，必须
   */
  generalOrderStatus: number;

}

export interface MaterialInfoDTO extends newsListMaterialParams {
  syncInfo: string // 是否同步资讯展示 首页跳转传0 资讯详情跳转传1
  page: number // 页码
  pageSize: number // 每页数量
  [key: string]: any // 其他参数
}
