/**
 * 内部系统标识转换为行为扩展字段中的内部系统字段值
 * @useCluesExtension
 */
export default () => {
  const kkInternalSystem = useCookie<string>(INNERNAL_SYSTEM)
  const extension1 = computed(() => {
    switch (Number(kkInternalSystem.value)) {
      case 26: // crm
        return '26'
        // case 31: // promotion
        //   return '2'
      case 33: // agent
        return '33'
      case 30: // promotionSass
        return '30'
      case 21: // 运营中心积分推广
        return '21'
      default:
        return undefined
    }
  })
  return { extension1 }
}
