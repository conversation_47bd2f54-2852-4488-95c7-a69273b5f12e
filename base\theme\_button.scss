@mixin BBT($border, $background, $color) {
  border-color: $border;
  background-color: $background;
  color: $color;
}

@mixin BBTLighten($color, $num, $num2) {
  border-color: lighten($color, $num);
  background-color: lighten($color, $num2);
  color: $color;
}

@include ns(btn) {
  @include BBT($borderColor, $backgroundColor, $textColor);
  display: inline-block;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid $borderColor;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  padding: 0 19px;
  border-radius: $borderRadius;
  font-size: 16px;
  transition: 0.2s;
  user-select: none;
  height: 40px;
  line-height: 38px;

  @include ns(icon) {
    margin-right: 9px;
    display: inline-block;
    vertical-align: middle;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: $disabledOpacity;
  }

  &.is-round {
    border-radius: 20px;

    &.large {
      border-radius: 12px;
    }

    &.middle {
      border-radius: 16px;
    }

    &.tiny {
      border-radius: 22px;
    }

    &.normal {
      border-radius: 8px;
    }

    &.small {
      border-radius: 6px;
    }

    /* &.mini {border-radius: 6px} */
  }

  &.is-circle {
    border-radius: 50%;
    height: 36px;
    padding: 0;
    width: 36px;
    line-height: 36px;

    &.large {
      width: 44px;
      height: 44px;
      line-height: 44px;
    }

    &.tiny {
      width: 44px;
      height: 44px;
      line-height: 44px;
    }

    &.small {
      width: 32px;
      height: 32px;
      line-height: 32px;
    }

    /* &.mini {width: 28px;height: 28px} */
  }

  /* &:hover {
    @include BBT($primaryColor, $default-btn-hover, $primaryColor);
    font-weight: 500;
  }

  &:active {
    @include BBT($primaryColor, $default-btn-active, $primaryColor);
    font-weight: 500;
  } */

  /*以上为默认*/

  /*要对每种type定义不同的状态，如type=primary*/
  &.#{$namespace}btn-primary {
    font-weight: 500;
    @include BBT(var(--kkc-brand), var(--kkc-brand), $textColor5);

    &:hover {
      @include BBT(var(--kkc-brand), var(--kkc-brand--hover), $textColor5);
      font-weight: 500;
    }

    &:active {
      @include BBT(var(--kkc-brand), var(--kkc-brand--active), $textColor5);
      font-weight: 500;
    }

    &.is-plain {
      @include BBTLighten($primaryColor, 30, 42);

      &:hover {
        @include BBT($primaryColor, $primaryColor, $textColor5);
        font-weight: 500;
      }
    }
  }

  &.#{$namespace}btn-info {
    @include BBT($infoColor, $infoColor, $fontColor-111);

    &:hover {
      @include BBT(var(--kkc-brand-light5), var(--kkc-brand-light5), var(--kkc-brand-text));
      font-weight: 500;
    }

    &:active {
      @include BBT(var(--kkc-brand-light5), var(--kkc-brand-light4), var(--kkc-brand-text));
      font-weight: 500;
    }

    &.is-plain {
      @include BBTLighten($infoPlainColor, 14, 100);

      &:active {
        @include BBT($infoActiveColor, $infoHoverColor, $primaryColor);
        font-weight: 500;
      }
    }
  }

  &.#{$namespace}btn-success {
    @include BBT($successColor, $successColor, $textColor5);

    &.is-plain {
      @include BBTLighten($successColor, 30, 58);

      &:hover {
        @include BBT($successColor, $successColor, $textColor5);
      }
    }
  }

  &.#{$namespace}btn-warning {
    @include BBT($warningColor, $warningColor, $textColor5);

    &.is-plain {
      @include BBTLighten($warningColor, 10, 26);

      &:hover {
        @include BBT($warningColor, $warningColor, $textColor5);
      }
    }
  }

  &.#{$namespace}btn-danger {
    @include BBT($dangerColor, $dangerColor, $textColor5);

    &.is-plain {
      @include BBTLighten($dangerColor, 30, 46);

      &:hover {
        @include BBT($dangerColor, $dangerColor, $textColor5);
      }
    }
  }

  &.#{$namespace}btn-text {
    //文字链接按钮
    border: 0;
    padding: 0;
    height: auto;
    line-height: normal;
  }

  // 尺寸大小
  &.large {
    padding: 0 14px;
    border-radius: 12px;
    font-size: 16px;
    height: 48px;
    line-height: 48px;
    font-weight: 500;
  }

  &.middle {
    // 按照手机2倍像素
    padding: 0 13px;
    border-radius: 16px;
    height: 44px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 500;
  }

  &.tiny {
    // 按照手机2倍像素
    padding: 0 13px;
    border-radius: 12px;
    height: 44px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 500;
  }

  &.normal {
    // 按照手机2倍像素
    padding: 0 13px;
    border-radius: 12px;
    height: 44px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 500;
  }

  &.small {
    padding: 0 11px;
    border-radius: 6px;
    font-size: 14px;
    height: 32px;
    font-weight: 400;
    line-height: 30px;
  }

  +.#{$namespace}btn {
    margin-left: 6px;
  }

  // loading动画
  .icon-loading {
    margin: 0 2px 0 0;
    position: relative;
    top: -2px;

    &:before {
      animation: iconLoading 2s linear infinite;
      display: block;
    }
  }
}

@include ns(btn-group) {}

@keyframes iconLoading {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}