export const CHINESE_NUMBERS = ['零', '一', '二', '三', '四']

/**
  * 1: "阿里app支付,APP支付"),
  * 2: "微信app支付"),
  * 3: "阿里当面付, 二维码支付"),
  * 4: "微信Native支付, 使用场景PC网站,生成二维码"),
  * 5: "全付通支付"),
  * 6: "微信jsapi支付，使用场景公众号"),
  * 7: "微信h5支付，移动端网页支付"),
  * 8: "微信小程序支付, 小程序支付"),
  * 9: "阿里电脑网站支付, pc网站"),
  * 10: "阿里手机网站支付，浏览器支付")
 * **/
export enum PayStatus {
  ALI_APP = 1,
  WX_APP,
  ALI_QRCODE,
  WX_NATIVE,
  QFT,
  WX_JSAPI,
  WX_H5,
  WX_MINI,
  ALI_PC,
  ALI_MOBILE,
}
// 订单状态
export enum OrderAction {
  PAY_NOW = 1, // 立即付款
  CANCEL_ORDER, // 取消订单 2
  APPLY_AFTER_SALE, // 申请售后 3
  CANCEL_AFTER_SALE, // 撤销申请 4
  VIEW_LOGISTICS, // 查看物流 5
  CONFIRM_RECEIPT, // 确认收货 6
  DELETE_ORDER, // 删除订单 7
  REORDER, // 再次购买 8
  VIEW_DETAILS, // 查看详情 9
  SHOW_ALL_STATUS_MODAL, // 全部状态弹框
  MODIFY_ADDRESS_MODAL, // 修改地址弹框
}

// 题库-题型状态
export enum ExamQuestionType {
  RADIO = 1, // 单选
  MULTIPLE, // 多选 2
  INDEFINITE, // 不定项 3
  FILL, // 填空题 4
  JUDGE, // 判断题 5
  SHORT_ANSWER, // 问答题 6
  MATERIAL, // 材料题 7
}

// 题库-答题答案状态
export enum ExamAnswerType {
  DEFAULT = 0, // 试题返回的初始默认状态
  SUBJECTIVE_UNFINISHED = 1, // 主观未做
  UNFINISHED, // 做了没评分 2
  CORRECT, // 对 3
  WRONG, // 错 4
  OBJECTIVE_UNFINISHED, // 客观未做 5
}

// 接口状态码
export enum NetCode {
  requireReLogin = '10041', // 重新登录
  loginExpired = '11105', // 重新登录
  findError = '10103', // 商品查询不到
  offShelfV2 = '21000', // 商品已下架"
  offShelf = '21001', // 商品已在该产品线下架
  notForSale = '21002' // 不可售
}

export const IS_LOCAL_ENV = (env: string) => ['LOCAL', 'LOCAL_STAGING', 'LOCAL_PRE', 'LOCAL_PRESSURE'].includes(env)
export const IS_DEBUG_ENV = (env: string) => ['LOCAL', 'LOCAL_STAGING', 'DEV', 'STAGING', 'LOCAL_PRESSURE', 'LOCAL_PRE', 'PRESSURE'].includes(env)
export const IS_DEV_ENV = (env: string) => ['LOCAL', 'DEV'].includes(env)
export const IS_STAGING_ENV = (env: string) => ['LOCAL_STAGING', 'STAGING'].includes(env)

export const LT_LOCAL_KEY = 'LT'
export const LT_SEARCH_KEY = 'lt'
//
export const PROMOTION_SHARE_KEY = 'kk-extension' // 推广标识
export const PROMOTION_SHARE_URL = 'kk-extension-url' // 推广链接
export const SYSTEM_TYPE = 'kk-system' // 系统
export const POOL_ID = 'poolId' // 客户池
export const SALE_ADMIN_ID = 'saleAdminId' // 销售id
export const TARGET_CODE = 'targetCode' // crm获客码标识
export const INNERNAL_SYSTEM = 'internalSystemId' // 内部系统标识
export const IS_AUDITION = 'audition' // crm试听商品标识
export const AUDITION_VALIDITY = 'tryListenValidityHour' // crm试听有效期标识（判断是否开通crm试听）
export const SHARE_PRICE = 'sharePrice' // crm 和 scrm 分享商品改价标识
export const SHARE_LINK_ID = 'shareId' // crm 和 scrm 分享商品链接id
export const LOC_SPEC_ID = 'locSpecId' // 未登录保存的多规格skuId
export const EXTERNAL_USER_ID = 'externalUserid' // scrm 外部联系人id

//
export enum DicSkipId {
  FUNCTION_LINK = 17,
  CUSTOM_LINK = 18,
  MINI_PROGRAM = 19,
  CUSTOM_PAGE = 29,
  // 在线客服
  ONLINE_KEFU = 31,
}

//
export const STUDY_TYPE__COURSE = 'course'
export const STUDY_TYPE__TEST_QUESTIONS = 'test-questions'
// LEARN_CENTER_TYPE
export const STUDY_TYPE = [
  {
    value: STUDY_TYPE__COURSE,
    label: '课程',
  },
  {
    value: STUDY_TYPE__TEST_QUESTIONS,
    label: '试题',
  },
]

/**
  * 行为记录枚举值 前端写死【看后续规划】
  * login  登录/注册操作
  * lock 用户浏览商品
  * CollectGoods 收藏商品
  * CreateOrder 下单
  * InviteFriendManage 邀请好友免费拿
  * RaffleManage 抽奖
  * PurchaseManage 加价购
  * PurchaseGiftManage 买赠
  * DistributionManage 分销
  * SeckillManagement 秒杀
  * groupManage 拼团
  * CouponManagement 优惠券
  * GiftPacks 大礼包
  * priceCoupon 有价优惠券
  * bargainPrice 砍价
  * answerLottery 答题抽奖
  * materialDownload 资料下载
 * **/

export enum ActionEnum {
  Login = '08de22374c9a4867a14255d8e262483d',
  Register = 'f945f9ef718e457eaf8ca2d5c757d7d9',
  Lock = '5c2b579f7b714520a10149eae9373fe0',
  CollectGoods = 'e01a4c72f9f54ac99490671088c6930e',
  CreateOrder = '3e4aa458d0e84ffdaa59b65f2be41e09',
  InviteFriendManage='b9b26deb649e4970b537130eedf42d0c',
  RaffleManage='2be043306e7247d298234b161b2c9678',
  PurchaseManage='d120a46b149e49129bebe68614c57e3c',
  PurchaseGiftManage='40336aa8ee8146eeb5ba1b4a06ef4750',
  DistributionManage='7161c86666634457bdb847899cec7ccf',
  SeckillManagement='696de1b977554fa1a4bd5057afc4fc44',
  groupManage='841e5a2d5e124d32841d35c2878316d0',
  CouponManagement='355c78e3628a43f5846373737db5c864',
  GiftPacks='f9ca11999c1a4eac9c937670234d802e',
  Question = '355a06fd3b744ac8ac51374bee631e9b',
  priceCoupon = '847fd00c9495494c94e306aa166f73fc',
  bargainPrice = 'f2f6858cac8e43f7bb675900d7eafbbc',
  answerLottery = '54707ff7c1034fdeb85507604476ca75',
  materialDownload = 'b78a7f9b339943848041d212ba75687a'
}

/**
 * 客户端类型
 */
export enum ClientType{
  PC = 1, // Pc
  H5, // H5
  ANDROID, // ANDROID
  IOS, // IOS
  WX_XCX, // 微信小程序
  DY_XCX, // 字节跳动小程序
  WAP, // wap
  XCX, // 小程序
  APP // app
}

/**
  * 用户来源 前端写死【看后续规划】
  * pc  来源id
  * wap 来源id
 * **/

export enum SourceEnum {
  Pc = '846548584211070976',
  Wap = '846548671976882176'
}

/**
  * 验证码模板
 * **/

export enum GetCodeType {
  login = 'login', // 登录
  forget = 'forgetPassword', // 忘记密码
  update = 'updatePassword', // 修改密码
  remove = 'removeUser', // 注销账号
  bind = 'thirdBindMobile', // 绑定手机号
}

/**
  * 订单状态
 * **/
export enum OrderStatus {
  UNPAID = 1, // 未付款
  PENDING_SHIPMENT = 2, // 待发货
  PENDING_RECEIPT = 3, // 待收货
  COMPLETED = 4, // 已完成
  TRADE_CLOSED = 5, // 交易关闭
  REFUND_UNDER_REVIEW = 6, // 退款审核中
  REVIEW_REJECTED = 7, // 审核驳回
  REFUND_SUCCESSFUL = 8, // 退款成功
  DISCOUNT_REVIEWING = 9, // 报名优惠审核中
  TRANSFERRING = 10, // 转班中
  TRANSFERRED = 11, // 已转班
  GROUP_BUYING = 12, // 拼团中
  PART_PAYMENT = 13, // 部分付款
}
/**
  * 做题状态
 * **/
export enum AnswerStatus {
  SUBJECTIVE_UNDONE = 1, // 主观未做
  DONE_NO_GRADE = 2, // 做了没评分
  CORRECT = 3, // 对
  WRONG = 4, // 错
  OBJECTIVE_UNDONE = 5 // 客观未做
}

/**
 * @enum BtnStatus
 * @description 按钮状态枚举，定义用户界面中按钮的不同状态及其对应的操作。
 * 包括以下状态：
 * - BuyNow (1): 立即购买
 * - ContinueBuy (2): 继续购买
 * - Buyed (3): 已购买，立即学习
 * - Learn (4): 立即领取
 * - Receive (5): 已领取
 * - LearnNow (6): 立即学习
 * - ComingSoon (7): 即将开售
 * - NotAvailable (8): 暂不可购买
 * - Overdue (9): 暂不可售
 */
export enum BtnStatus {
  BuyNow = 1, // 立即购买
  ContinueBuy, // 继续购买
  Buyed, // 已购买，立即学习
  Learn, // 立即领取
  Receive, // 已领取
  LearnNow, // 立即学习
  ComingSoon, // 即将开售
  NotAvailable, // 暂不可购买
  Overdue, // 暂不可售
}

/**
 * @enum ActivityType
 * @description 营销活动类型枚举，用于标识不同的营销活动。
 * @property {number} Group - 拼团活动
 * @property {number} Seckill - 秒杀活动
 * @property {number} Distribution - 分销活动
 * @property {number} Bargin - 砍价活动
 *
 */
export enum ActivityType {
  Group = 1, // 拼团
  Seckill, // 秒杀
  Distribution, // 分销
  Bargin = 12
}

/**
 * @constant ActivityTypeName
 * @description 营销活动名称数组，与 ActivityType 枚举一一对应。
 */
// export const ActivityTypeName = ['拼团', '秒杀', '分销']
export const ActivityTypeName = new Map([
  [ActivityType.Group, '拼团'],
  [ActivityType.Seckill, '秒杀'],
  [ActivityType.Distribution, '分销'],
  [ActivityType.Bargin, '砍价']
])
/**
 * @enum MarketingOrderTagStatus
 * @description 营销订单标签状态枚举，定义订单的营销标签类型。
 * @property {number} BuyGive - 买赠活动
 * @property {number} Purchase - 加购活动
 */
export enum MarketingOrderTagStatus {
  BuyGive = 3, // 买赠
  Purchase = 4, // 加购
}

/**
 * @constant MarketingOrderTagMap
 * @description 营销订单标签的映射关系，定义状态值与对应描述之间的关系。
 */
export const MarketingOrderTagMap = new Map([
  [3, '赠品'],
  [4, '加购'],
])

/**
 * @enum ExciseLabelEnum
 * @description 题库分类及标签枚举，定义不同层次分类和属性标签。
 * @property {string} studyLevel1 - 一级分类
 * @property {string} studyLevel2 - 二级分类
 * @property {string} studyLevel3 - 三级分类
 * @property {string} studyLevel4 - 四级分类
 * @property {string} academicSection - 学段
 * @property {string} directionType - 方向
 * @property {string} examFormat - 考试形式
 * @property {string} subjectType - 科目
 * @property {string} region - 地区
 */
export enum ExciseLabelEnum {
  studyLevel1 = '一级分类',
  studyLevel2 = '二级分类',
  studyLevel3 = '三级分类',
  studyLevel4 = '四级分类',
  academicSection = '学段',
  directionType = '方向',
  examFormat = '考试形式',
  subjectType = '科目',
  region = '地区',
}

/**
 * @enum productSideEnum
 * @description 产品端枚举，表示来源产品端的类型。
 * @property {number} pc - PC 端
 * @property {number} miniProgram - 小程序
 * @property {number} wap - WAP 端
 * @property {number} app - 移动应用
 */
export enum productSideEnum {
  pc = 1, // PC 端
  miniProgram = 5, // 小程序
  wap = 7, // WAP 端
  app = 9, // 移动应用
}

/**
 * @enum BooleanEnum
 * @description 布尔值枚举，用于表示后端返回的逻辑值。
 * @property {number} TRUE - 真 (1)
 * @property {number} FALSE - 假 (0)
 */
export enum BooleanEnum {
  TRUE = 1, // 真
  FALSE = 0, // 假
}

/***
 * 题库-做题模式
 *
 * @EXCISE 1 练习模式
 * @EXAM 2 考试模式正计时
 * @QUICK 3 快答模式
 * @RECITE 4 背题模式
 * @EXAM_REVERS 5 考试模式倒计时
 */
export enum ExciseModeEnum {
  EXCISE = 1, // 练习模式
  EXAM, // 考试模式正计时
  QUICK, // 快答模式
  RECITE, // 背题模式
  EXAM_REVERS // 考试模式倒计时
}
