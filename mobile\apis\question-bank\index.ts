import filterQuestionBankBody from '../../../base/utils/filterBody'

import type {
  TextBookParams,
  PaperDetailType,
  QueryModuleListParams, ModuleList, TargetResultId, QueryModuleCategoryListParams, StartType,
  submitLeaveType,
  submitFinishType,
  submitQuestType,
  finishSubmitQuestType,
  DoNextRoundProtBodyType,
  RankProtBodyType,
  QuestionCountForQuestionType,
  // QuestionAddUserCollectBodyType,
  QuestionAddUserCollectV2BodyType,
  QuestionDeleteUserCollectBodyType,
  QuestionErrorBaseType,
  QuestionErrorSubmitType,
  QuestionLogPageBodyType,
  QuestionLogLeaveBodyType,
  QuestionLogSubmitBodyType,
  QuestionLogRemoveBodyType,
  StartProtParams,
  DoQuestionListBodyType,
  DoQuestionProtBodyType,
  SaveUserCategoryModuleTagBodyType,
  GetModuleStudyLevelCategoryTagsListBodyType,
  GetModuleManageTagsListBodyType,
  FixedStartProtParams,
  QuestionListParams,
  FixedCatalogueParams,
  GetModuleManagePrivilegeType,
  GetBookPrivilegeType,
  FeeFirstAddBodyType,
  FeeDelBodyType,
  FeeOtherAddBodyType,
  FeeEditBodyType,
  FeeSubmitBodyType,
  QuestionAnswerCardParamsType,
  QuestionDetailParamsType,
  chapterSubmitQuestType,
  QuestionDetailListParamsType,
  ModuleManageTagsType,
  CoreGoodsPermissionType
} from './types'

enum QuestioBankApi {
  queryModuleList = '/kukecorequestion/wap/moduleManageTags/queryModule',
  queryModuleCategoryList = '/kukecorequestion/wap/moduleManageTags/moduleCategoryList',
  queryModuleAllStudyList = '/kukecorequestion/wap/moduleManageTags/moduleStudyLevelList',
  updateMajorInfoData = '/kukecorequestion/wap/moduleManageTags/aoeUserTagsProt',
  fetchCategoryListByModuleId = '/kukecorequestion/wap/moduleManageTagsV1/getStudyLevelList',
  getModuleCategoryTagsList = '/kukecorequestion/wap/moduleManageTags/moduleTagsList',
  getReviewQuestionsModel = '/kukecorequestion/wap/daily/getReviewQuestionsModel',
  doQuestionTypeProt = '/kukecorequestion/wap/daily/doQuestionTypeProt',
  getDailyPracticeList = '/kukecorequestion/wap/daily/getDailyPracticeList',
  getTestpaperList = '/kukecorequestion/wap/testpaper/list',
  getTextbookList = '/kukecorequestion/wap/chapter/textbookList',
  getChapterList = '/kukecorequestion/wap/chapter/list',
  checkModuleManageProt = '/kukecorequestion/wap/daily/checkModuleManageProt',

  // 章节练习
  chapterStart = '/kukecorequestion/wap/chapter/startProt',
  chapterDoContinue = '/kukecorequestion/wap/chapter/doContinueProt',
  chapterDoAgain = '/kukecorequestion/wap/chapter/doAgainProt',
  chapterSubmitQuestion = '/kukecorequestion/wap/chapter/submitQuestionProt',
  chapterSubmitLeave = '/kukecorequestion/wap/chapter/submitLeaveProt',
  chapterSubmitFinish = '/kukecorequestion/wap/chapter/submitFinishProt',
  chapterReportProt = '/kukecorequestion/wap/chapter/reportProt',
  chapterDoNextRoundProt = '/kukecorequestion/wap/chapter/doNextRoundProt',
  // 模考
  getTestPaperInfo = '/kukecorequestion/wap/testpaper/getInfo',
  // 学习报告
  getReportInfo = '/kukecorequestion/wap/questionRank/studyReportProt',
  // 排行榜
  getRankList = '/kukecorequestion/wap/questionRank/getRankListProt',
  // 错题本收藏本历史记录本
  querySortLabelProt = '/kukecorequestion/wap/questioncount/querySortLabelsProt',
  getTotalNumQuestion = '/kukecorequestion/wap/questioncount/totalNumberQuestions',
  getTotalNumQuestionTypeProt = '/kukecorequestion/wap/questioncount/questionTypeSummaryProt',
  // 纠错类型
  getCorrectTypeProt = '/kukecorequestion/wap/baseType/listsProt',
  // 纠错提交
  doCorrectQuestionProt = '/kukecorequestion/wap/kqQuestion/addFeedbackProt',
  // 收藏本
  addUserCollectQuestionProt = '/kukecorequestion/wap/collect/addUserCollectProt',
  removeUserCollectQuestionProt = '/kukecorequestion/wap/collect/removeUserCollectProt',
  getUserCollectQuestionProt = '/kukecorequestion/wap/collect/showQuestionProt',
  doCollectQuestionProt = '/kukecorequestion/wap/collect/doQuestionProt',
  doAgainCollectQuestionProt = '/kukecorequestion/wap/collect/doAgainProt',
  submitCollectQuestionProt = '/kukecorequestion/wap/collect/submitQuestionProt',
  submitLeaveCollectQuestionProt = '/kukecorequestion/wap/collect/submitLeaveProt',
  // 收藏试题 v2
  addUserCollectQuestionProtV2 = '/kukecorequestion/wap/collect/addUserCollectV1Prot',
  // 错题本
  getWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/showQuestionProt',
  doWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/doQuestionProt',
  doAgainWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/doAgainProt',
  submitWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/submitQuestionProt',
  submitLeaveWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/submitLeaveProt',
  removeWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/removeQuestionProt',
  // 练习历史
  getHistoryQuestionProt = '/kukecorequestion/wap/questionglog/showQuestionProt',
  doHistoryQuestionProt = '/kukecorequestion/wap/questionglog/doQuestionProt',
  doAgainHistoryQuestionProt = '/kukecorequestion/wap/questionglog/doAgainProt',
  submitHistoryQuestionProt = '/kukecorequestion/wap/questionglog/submitQuestionProt',
  submitLeaveHistoryQuestionProt = '/kukecorequestion/wap/questionglog/submitLeaveProt',
  removeHistoryQuestionProt = '/kukecorequestion/wap/questionglog/removeQuestionProt',
  // 估分试卷
  subscribeEstimateQuestionProt = '/kukecorequestion/wap/gufen/subscribeProt',
  estimateStart = '/kukecorequestion/wap/gufen/startProt',
  estimateDoAgain = '/kukecorequestion/wap/gufen/doAgainProt',
  estimateDoContinue = '/kukecorequestion/wap/gufen/doContinueProt',
  estimateSubmitFinish = '/kukecorequestion/wap/gufen/submitFinishProt',
  estimateReport = '/kukecorequestion/wap/gufen/reportProt',
  // 估分邀请详情页
  estimateInviteDetail = '/kukecorequestion/wap/gufen/detail',
  // 题库组件化
  moduleCategoryListTree = '/kukecorequestion/wap/moduleManageTags/moduleStudyLevelListTree',
  // 刷题数据统计
  getQuestionCountProt = '/kukecorequestion/wap/questionRank/getQuestionCountProt',
  // 保存用户分类模块标签
  saveUserCategoryProt = '/kukecorequestion/wap/moduleManageTags/saveUserGoalsProt',
  // 获取用户分类模块标签
  getUserCategoryProt = '/kukecorequestion/wap/moduleManageTags/getUserGoalsProt',
  // 获取产品线专业分类下关联的标签
  getModuleStudyLevelCategoryTagsList = '/kukebasesystem/wap/productCategory/productCategoryLabelValueList',
  // 获取模块标签列表（新分类改造需求 v3.0.3）
  getModuleManageTagsList = '/kukecorequestion/wap/moduleManageTagsV1/getTagLists',
  // 获取用户分类模块标签（新分类改造需求 v3.0.3）
  getUserCategoryV3Prot = '/kukecorequestion/wap/moduleManageTagsV1/getUserGoalsProt',
  // 保存用户分类模块标签（新分类改造需求 v3.0.3）
  saveUserCategoryV3Prot = '/kukecorequestion/wap/moduleManageTagsV1/addUserGoalsProt',
  // 人气统计埋点
  buriedPopularForQuestion = '/kukecorequestion/wap/testpaper/doClick',
  // 固定刷题
  getFixedQuestionList = '/kukecorequestion/wap/brush/list',
  getFixedQuestionType = '/kukecorequestion/wap/brush/getQuestionType',
  // getFixedQuestionAnswerCard = '/kukecorequestion/wap/brushChapter/answerCardProt',
  getFixedQuestionReport = '/kukecorequestion/wap/brushChapter/reportProt',
  getFixedQuestionReportStatics = '/kukecorequestion/wap/brush/reportStaticsProt',
  getFixedQuestionCatalogue = '/kukecorequestion/wap/brush/info',
  fixedQuestionStart = '/kukecorequestion/wap/brushChapter/startProt',
  fixedQuestionDoAgain = '/kukecorequestion/wap/brushChapter/doAgainProt',
  fixedQuestionDoContinue = '/kukecorequestion/wap/brushChapter/doContinueProt',
  fixedQuestionSubmit = '/kukecorequestion/wap/brushChapter/submitQuestionProt',
  fixedQuestionSubmitLeave = '/kukecorequestion/wap/brushChapter/submitLeaveProt',
  fixedQuestionSubmitFinish = '/kukecorequestion/wap/brushChapter/submitFinishProt',
  fixedQuestionDoClickCount = '/kukecorequestion/wap/brush/doClick',
  // 判断模块权益
  getModulePrivilege = '/kukecorequestion/kqQuestionRight/judgeModuleRight',
  // 获取教材权益
  getTextBookPrivilege = '/kukecorequestion/wap/chapter/textbookRightType',
  // 收费设置
  feeFirstAddProt = '/kukecorequestion/wap/fee/firstAddProt', // 初始化获取下单列表
  feeDetailProt = '/kukecorequestion/wap/fee/detailProt', // 通过记录id获取下单列表
  feeOtherAddProt = '/kukecorequestion/wap/fee/otherAddProt', // 非第一次添加商品计算价格
  feeDelProt = '/kukecorequestion/wap/fee/delProt', // 删除商品计算价格
  feeEditProt = '/kukecorequestion/wap/fee/editProt', // 编辑商品计算价格
  // 提交订单
  feeSubmitProt = '/kukeonlineorder/wap/order/addQuestionOrderProt',
  // 获取答题卡数据
  getFixedQuestionAnswerCard = '/kukecorequestion/wap/testpaper/answerCardProt',
  // 判断固定刷题目录是否存在
  getFixedQuestionExistBrush = '/kukecorequestion/wap/brush/existBrush',
  // 获取答题卡
  getQuestionAnswerCard = '/kukecorequestion/wap/chapter/answerCardProt',
  // 获取试题详情
  getQuestionDetail = '/kukecorequestion/wap/chapter/getQuestionDetailProt',
  // 批量获取试题详情
  getQuestionDetailList = '/kukecorequestion/wap/chapter/getQuestionBatchProt',
  // 获取模块管理标签列表  题库首页使用
  getHomeTagLists = '/kukecorequestion/wap/moduleManageTagsV1/getTagListsV1',
  // 获取试题信息
  getQuestionInfo = '/kukecorequestion/wap/question/getQuestionInfo',
  // 获取试卷信息
  getPaperInfo = '/kukecorequestion/wap/testpaper/getInfoV1',
  // 获取模块详情
  getModuleDetail = '/kukecorequestion/wap/moduleManageTagsV1/getModuleInfo',
  // 判断商品中心试卷包权益
  getCoreGoodsPermissionProt = '/kukecorequestion/outer/coreGoods/getCoreGoodsPermissionProt',
  // 获取试卷推广详情
  getPaperTagInfo = '/kukecorequestion/wap/testpaper/getInfoTG',
  // 获取落地页信息
  getLandingPageInfo = '/kukecorequestion/wap/testpaper/getLandPageInfo',
  // 【新】获取试卷详情（包括试卷推广详情）
  getPaperDetail = '/kukecorequestion/wap/goods/getTestpaperInfo',
  // 【新】刷题版本详情
  getQuestionBrushInfo = '/kukecorequestion/wap/goods/getBrushInfo',
  // 教材单品详情
  getTextBookDetail = '/kukecorequestion/wap/goods/getTextbookInfo',
  // 获取状态分类
   getModuleInfoAndStudyLevelName= '/kukecorequestion/wap/moduleManageTagsV1/getModuleInfoAndStudyLevelName'
}

// common
export * from './module/common'
// 学服相关
export * from './module/study-plan'
// 标签
export * from './module/tag'
// 试卷相关
export * from './module/paper'
// 每日一练
export * from './module/daily'

// 获取模块列表数据
export async function getModuleListData (body: QueryModuleListParams) {
  return useHttp<ModuleList>(QuestioBankApi.queryModuleList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 模块分类查询_pc
export async function getModuleCategoryList (body: QueryModuleCategoryListParams) {
  return useHttp<any>(QuestioBankApi.queryModuleCategoryList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 通过模块id请求分类列表
 * @param body
 * @returns
 */
export async function fetchCategoryListByModuleId (body: {moduleManageId:string}) {
  return useHttp<any>(QuestioBankApi.fetchCategoryListByModuleId, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 获取所有模块专业分类
export async function getModuleAllStudyList (body: any) {
  return useHttp<any>(QuestioBankApi.queryModuleAllStudyList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 用户分类添加或修改_pc
export async function updateMajorInfoData (body: any) {
  return useHttp<any>(QuestioBankApi.updateMajorInfoData, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 模块分类标签查询_pc
export async function getModuleCategoryTagsList (body: QueryModuleCategoryListParams) {
  return useHttp<any>(QuestioBankApi.getModuleCategoryTagsList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
// 获取刷题模式-pc wap app
export async function getReviewQuestionsModel (body: any) {
  return useHttp<any>(QuestioBankApi.getReviewQuestionsModel, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 设置刷题模式-pc wap app
export async function doQuestionTypeProt (body: any) {
  return useHttp<any>(QuestioBankApi.doQuestionTypeProt, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 每日练习列表-pc wap app
export async function getDailyPracticeList (body: DoQuestionListBodyType) {
  return useHttp<any>(QuestioBankApi.getDailyPracticeList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: false,
    watch: false
  })
}

// 试卷列表-pc wap app
export async function getTestpaperList (body: DoQuestionListBodyType) {
  return useHttp<any>(QuestioBankApi.getTestpaperList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: false,
    watch: false
  })
}

// 教材列表-pc wap app
export async function getTextbookList (body: any) {
  return useHttp<any>(QuestioBankApi.getTextbookList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: true,
    watch: false
  })
}

// 章节列表-pc wap app
export async function getChapterList (body: any) {
  return useHttp<any>(QuestioBankApi.getChapterList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: false,
    watch: false
  })
}

// 查询模块是否存在-pc wap app
export async function checkModuleManageProt (body: any) {
  return useHttp<any>(QuestioBankApi.checkModuleManageProt, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 章节练习---开始练习
export async function getChapterStart (body: any) {
  return useHttp<any>(QuestioBankApi.chapterStart, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 章节练习---继续练习
export async function getChapterDoContinue (body: any) {
  return useHttp<any>(QuestioBankApi.chapterDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 章节练习---再次练习
export async function getChapterDoAgain (body: any) {
  return useHttp<any>(QuestioBankApi.chapterDoAgain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 章节练习---试题提交（一提一提交）
export async function chapterSubmitQuestion (body: submitQuestType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(QuestioBankApi.chapterSubmitQuestion, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

// 章节练习---中途提交
export async function chapterSubmitLeave (body: chapterSubmitQuestType) {
  return useHttp<any>(QuestioBankApi.chapterSubmitLeave, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false,
  })
}

// 章节练习---完成提交
export async function chapterSubmitFinish (body: finishSubmitQuestType) {
  return useHttp<any>(QuestioBankApi.chapterSubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 章节练习---报告页
export async function getChapterReportProt (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.chapterReportProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 章节练习---开启下一轮
export async function chapterDoNextRoundProt (body: DoNextRoundProtBodyType) {
  return useHttp<any>(QuestioBankApi.chapterDoNextRoundProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取模考考试说明

export async function getTestPaperInfo (body: any) {
  return useHttp<any>(QuestioBankApi.getTestPaperInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取题库个人学习报告
export async function getReportPort (body: any) {
  return useHttp<any>(QuestioBankApi.getReportInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取排行榜列表
export async function getRankingList (body: RankProtBodyType) {
  return useHttp<any>(QuestioBankApi.getRankList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 三个本分类以及标签列表
export async function getSortLabelProt () {
  return useHttp<any>(QuestioBankApi.querySortLabelProt, {
    method: 'post',
    transform: res => res.data,
  })
}

// 三个本 - 数量统计
export async function getTotalNumQuestion () {
  return useHttp<any>(QuestioBankApi.getTotalNumQuestion, {
    method: 'post',
    transform: res => res.data,
  })
}

// 三个本 - 试题类型下题数统计
export async function getTotalNumQuestionTypeProt (body: QuestionCountForQuestionType) {
  return useHttp<any>(QuestioBankApi.getTotalNumQuestionTypeProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 收藏试题
export async function addUserCollectQuestionProt (body: QuestionAddUserCollectV2BodyType) {
  return useHttp<any>(QuestioBankApi.addUserCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

export async function addUserCollectQuestionProtV2 (body: QuestionAddUserCollectV2BodyType) {
  return useHttp<any>(QuestioBankApi.addUserCollectQuestionProtV2, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 取消收藏
export async function deleteUserCollectQuestionProt (body: QuestionDeleteUserCollectBodyType) {
  return useHttp<any>(QuestioBankApi.removeUserCollectQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取纠错类型
export async function getCorrectTypeProt (body: QuestionErrorBaseType) {
  return useHttp<any>(QuestioBankApi.getCorrectTypeProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 纠错提交
export async function doCorrectQuestionProt (body: QuestionErrorSubmitType) {
  return useHttp<any>(QuestioBankApi.doCorrectQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 收藏列表
export async function getUserCollectQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.getUserCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 收藏刷题列表
export async function doCollectQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.doCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 收藏重新刷题
export async function doAgainCollectQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.doAgainCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 收藏提交
export async function submitCollectQuestionProt (body: QuestionLogSubmitBodyType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(QuestioBankApi.submitCollectQuestionProt, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

// 收藏离开
export async function submitLeaveCollectQuestionProt (body: QuestionLogLeaveBodyType) {
  return useHttp<any>(QuestioBankApi.submitLeaveCollectQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 错题列表
export async function getWrongLogQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.getWrongLogQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 错题刷题列表
export async function doWrongLogQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.doWrongLogQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 错题重新刷题
export async function doAgainWrongLogQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.doAgainWrongLogQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 错题提交
export async function submitWrongLogQuestionProt (body: QuestionLogSubmitBodyType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(QuestioBankApi.submitWrongLogQuestionProt, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

// 错题离开
export async function submitLeaveWrongLogQuestionProt (body: QuestionLogLeaveBodyType) {
  return useHttp<any>(QuestioBankApi.submitLeaveWrongLogQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 移除错题
export async function removeWrongLogQuestionProt (body: QuestionLogRemoveBodyType) {
  return useHttp<any>(QuestioBankApi.removeWrongLogQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data
  })
}

// 练习历史列表
export async function getHistoryQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.getHistoryQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 练习历史列表
export async function doHistoryQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.doHistoryQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 练习历史刷题
export async function doAgainHistoryQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(QuestioBankApi.doAgainHistoryQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 练习历史提交
export async function submitHistoryQuestionProt (body: QuestionLogSubmitBodyType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(QuestioBankApi.submitHistoryQuestionProt, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

// 练习历史离开
export async function submitLeaveHistoryQuestionProt (body: QuestionLogLeaveBodyType) {
  return useHttp<any>(QuestioBankApi.submitLeaveHistoryQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 移除练习历史
export async function removeHistoryQuestionProt (body: QuestionLogRemoveBodyType) {
  return useHttp<any>(QuestioBankApi.removeHistoryQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data
  })
}
// 估分试卷---预约估分
export async function estimateSubscribe (body: StartType) {
  return useHttp<any>(QuestioBankApi.subscribeEstimateQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data
  })
}
// 估分试卷---开始做题
export async function estimateStart (body: StartType) {
  return useHttp<any>(QuestioBankApi.estimateStart, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}
// 估分试卷---再次练习
export async function estimateDoAgain (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.estimateDoAgain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 估分试卷---继续做题
export async function estimateDoContinue (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.estimateDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 估分试卷---完成提交
export async function estimateSubmitFinish (body: submitFinishType) {
  return useHttp<any>(QuestioBankApi.estimateSubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}
// 估分试卷---查看报告
export async function estimateReport (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.estimateReport, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 估分试卷---好友邀请详情页
export async function getEstimateInviteDetail (body: StartProtParams) {
  return useHttp<any>(QuestioBankApi.estimateInviteDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 题库组件化---专业分类
export async function getModuleCategoryListTree (body: any) {
  return useHttp<any>(QuestioBankApi.moduleCategoryListTree, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 题库刷题统计
export async function getQuestionCountProt (body: any) {
  return useHttp<any>(QuestioBankApi.getQuestionCountProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取固定刷题列表
export async function getFixedQuestionListProt (body: QuestionListParams) {
  return useHttp<any>(QuestioBankApi.getFixedQuestionList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

// 固定刷题-获取刷题模式
export async function getFixedQuestionTypeProt (body: { moduleManageId: string }) {
  return useHttp<any>(QuestioBankApi.getFixedQuestionType, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-获取答题卡数据
export async function getFixedQuestionAnswerCardProt (body: TargetResultId & { model: number }) {
  return useHttp<any>(QuestioBankApi.getFixedQuestionAnswerCard, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-开始做题
export async function startFixedQuestionProt (body: FixedStartProtParams) {
  return useHttp<any>(QuestioBankApi.fixedQuestionStart, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-再次做题
export async function againFixedQuestionProt (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.fixedQuestionDoAgain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-继续做题
export async function continueFixedQuestionProt (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.fixedQuestionDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-一题一提交
export async function submitFixedQuestionProt (body: submitQuestType) {
  return useHttp<any>(QuestioBankApi.fixedQuestionSubmit, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-中途提交/暂存
export async function fixedSubmitLeave (body: submitLeaveType) {
  return useHttp<any>(QuestioBankApi.fixedQuestionSubmitLeave, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-交卷
export async function fixedSubmitFinish (body: submitFinishType) {
  return useHttp<any>(QuestioBankApi.fixedQuestionSubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 固定刷题-点击人次统计
export async function fixedQuestionClickStatistics (body: DoQuestionProtBodyType) {
  return useHttp<any>(QuestioBankApi.fixedQuestionDoClickCount, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 保存用户分类模块标签
export async function saveUserCategoryModuleTag (body: SaveUserCategoryModuleTagBodyType) {
  return useHttp<any>(QuestioBankApi.saveUserCategoryProt, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 获取用户分类模块标签
export async function getUserCategoryModuleTag () {
  return useHttp<any>(QuestioBankApi.getUserCategoryProt, {
    method: 'post',
    transform: res => res.data,
    watch: false
  })
}

// 获取产品线分类下标签
export async function getModuleStudyLevelCategoryTagsList (body: GetModuleStudyLevelCategoryTagsListBodyType) {
  return useHttp<any>(QuestioBankApi.getModuleStudyLevelCategoryTagsList, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 获取模块标签列表
export async function getModuleManageTagsList (body: GetModuleManageTagsListBodyType) {
  return useHttp<any>(QuestioBankApi.getModuleManageTagsList, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 获取用户最后一次记录
export async function getUserCategoryV3Prot () {
  return useHttp<any>(QuestioBankApi.getUserCategoryV3Prot, {
    method: 'post',
    transform: res => res.data,
    watch: false
  })
}

// 保存用户分类模块标签
export async function saveUserCategoryV3Prot (body: GetModuleManageTagsListBodyType) {
  return useHttp<any>(QuestioBankApi.saveUserCategoryV3Prot, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 试卷点击埋点
export async function buriedPopularForQuestion (body: DoQuestionProtBodyType & {examMode: number}) {
  return useHttp<any>(QuestioBankApi.buriedPopularForQuestion, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}
// 固定刷题-报告
export async function getFixedQuestionReport (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.getFixedQuestionReport, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 固定刷题-报告统计
export async function getFixedQuestionReportStatics (body: TargetResultId) {
  return useHttp<any>(QuestioBankApi.getFixedQuestionReportStatics, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 固定刷题-目录
export async function getFixedQuestionCatalogue (body: FixedCatalogueParams) {
  return useHttp<any>(QuestioBankApi.getFixedQuestionCatalogue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 获取模块权益
export async function getModulePrivilege (body: GetModuleManagePrivilegeType) {
  return useHttp<any>(QuestioBankApi.getModulePrivilege, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 获取教材权益
export async function getTextBookPrivilege (body: GetBookPrivilegeType) {
  return useHttp<any>(QuestioBankApi.getTextBookPrivilege, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 第一次添加商品计算价格
export async function feeFirstAddProt (body: FeeFirstAddBodyType) {
  return useHttp<any>(QuestioBankApi.feeFirstAddProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 费用详情
export async function feeDetailProt (body: QuestionLogRemoveBodyType & { expiry?: number }) {
  return useHttp<any>(QuestioBankApi.feeDetailProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 删除
export async function feeDelProt (body: FeeDelBodyType) {
  return useHttp<any>(QuestioBankApi.feeDelProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 非第一次添加商品计算价格
export async function feeOtherAddProt (body: FeeOtherAddBodyType) {
  return useHttp<any>(QuestioBankApi.feeOtherAddProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 编辑商品计算价格
export async function feeEditProt (body: FeeEditBodyType) {
  return useHttp<any>(QuestioBankApi.feeEditProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 提交订单
export async function feeSubmitProt (body: FeeSubmitBodyType) {
  return useHttp<any>(QuestioBankApi.feeSubmitProt, {
    method: 'post',
    body,
  })
}

// 判断固定刷题目录是否存在
export async function getFixedQuestionExistBrush (body: { brushChapterId: string, moduleManageId: string }) {
  return useHttp<any>(QuestioBankApi.getFixedQuestionExistBrush, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取答题卡
export async function getQuestionAnswerCard (body: QuestionAnswerCardParamsType) {
  return useHttp<any>(QuestioBankApi.getQuestionAnswerCard, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取试题详情
export async function getQuestionDetail (body: QuestionDetailParamsType) {
  return useHttp<any>(QuestioBankApi.getQuestionDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 批量获取试题详情
export async function getQuestionDetailList (body: QuestionDetailListParamsType) {
  return useHttp<any>(QuestioBankApi.getQuestionDetailList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 题库首页标签列表
export async function getModuleManageHomeTags (body: ModuleManageTagsType) {
  return useHttp<any>(QuestioBankApi.getHomeTagLists, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取试题信息
export async function getQuestionInfo (body: { questionId: string }) {
  return useHttp<any>(QuestioBankApi.getQuestionInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取试卷信息
export async function getPaperInfo (body: StartType) {
  return useHttp<any>(QuestioBankApi.getPaperInfo, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 【新】获取试卷详情
 */
export async function getPaperDetail (body: PaperDetailType) {
  return useHttp<any>(QuestioBankApi.getPaperDetail, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 获取模块详情
 */
export async function getModuleDetail (body: { moduleManageId: string }) {
  return useHttp<any>(QuestioBankApi.getModuleDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
/**
 * 判断商品中心试卷包权益
 */
export async function getGoodsPermissionProt (body: CoreGoodsPermissionType) {
  return useHttp<any>(QuestioBankApi.getCoreGoodsPermissionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取试卷标签详情
 */
export async function getPaperTagInfo (body: {testpaperId: string, moduleManageId: string, goodsMasterId: string}) {
  return useHttp<any>(QuestioBankApi.getPaperTagInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取落地页信息
 */
export async function getLandingPageInfo (body: {moduleManageId: string, testpaperId: string}) {
  return useHttp<any>(QuestioBankApi.getLandingPageInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取刷题版本下目录
 */
export async function getQuestionBrushInfo (body: FixedCatalogueParams) {
  return useHttp<any>(QuestioBankApi.getQuestionBrushInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取单品教材下章节
 */
export async function getTextBookDetail (body: TextBookParams) {
  return useHttp<any>(QuestioBankApi.getTextBookDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
/**
 * 获取状态
 */
export async function getModuleInfoAndStudyLevelName (body: any) {
  return useHttp<any>(QuestioBankApi.getModuleInfoAndStudyLevelName, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
