import type { IDeliveryInfoParams } from '@/apis/order/types'

/**
 * 营销活动类型
 */
export enum MarketingActivityTypeEnum {
    GroupBuying = 1, // 拼团
    SecKill = 2, // 秒杀
    Distribution = 3, // 分销
    MarkupPurchase = 4, // 加价购
    BuyGift = 5 // 买赠
}

/**
 * 请求加入购物车参数
 */
export interface FetchAddCartParams {
    goodsMasterId: string; // 商品主 id，必须
    specificationItemId?: string; // 子规格 id，非必须
    quantity: number; // 数量，必须
}

/**
 * 请求删除购物车参数
 */
export interface FetchDeleteCartParams {
    ids: string[]; // 购物车主键 id 集合，必须
}

/**
 * 请求收藏购物车商品参数
 */
export interface FetchCollectCartParams {
    ids: string[]; // 购物车主键 id 集合，必须
}

/**
 * 请求修改购物车数量参数
 */
export interface FetchUpdateCartQuantityParams {
    id: string; // 用户购物车主键 id，必须
    quantity: number; // 加减后的数量，必须
    isCheck?:boolean;// 接口其实不需要 只是前端用此来判断当前修改数量的商品项有没有勾选 勾选状态 才调用计算价格
    goodsOrdinary:boolean;// 是否为普通商品，如果为普通商品 true，则不再请求营销中心获取营销中心活动信息
}

/**
 * 请求修改购物车商品规格参数
 */
export interface FetchUpdateCartSpecParams {
    id: string; // 用户购物车主键 id，必须
    goodsMasterId: string; // 商品主 id，必须
    specificationItemId: string; // 子规格商品 id，必须
    quantity: number; // 数量，必须
}

/**
 * 购物车列表-请求参数
 */
export interface FetchCartListParams {
    deliveryInfo?: IDeliveryInfoParams, // 收货信息
}

/**
 * 计算购物车商品所需参数
 */
export interface CalcCartGoodsParam {
    id:string // 购物车主键 id 集合，必须
    quantity: number // 数量，必须
    activityTypes?: MarketingActivityTypeEnum[]; // 营销活动类型 1-拼团 2-秒杀 3-分销 4-加价购 5-买赠，必须
}

/**
 * 请求计算购物车价格信息参数
 */
export interface FetchCalcCartPriceInfoParams {
    goodsList: CalcCartGoodsParam[];
}

/**
 * Sku 类型
 */
export interface Sku {
    value: string; // 规格名称，非必须
    name: string; // 规格项名称，非必须
}

/**
 * 购物车商品状态
 */
export enum GoodsStatusEnum {
    Normal = 1, // 正常
    Offline = 2, // 下架
    Prohibited = 3, // 禁售
    Deleted = 4, // 商品删除
    Unsold = 5, // 未开售
    NotSupportedInCurrentRegion = 6 // 当前地区不支持配送
}

/**
 * 班型
 */
export interface IClassroomType {
    classroomTypeName: string; // 班型名称，必须
    fontColor: string; // 班型字体颜色，必须
    bgColor: string; // 班型背景颜色，必须
}

/**
 * 购物车商品单项DTO
 */
export interface CartGoodsItemDTO {
    cartId: string; // 购物车id，必须
    skus?: Sku[]; // item 类型: object，必须
    classroomType: IClassroomType; // 班型，必须
    goodsImg: string; // 商品图片，必须
    goodsTitle: string; // 商品名称，必须
    quantity: number; // 数量，必须
    goodsPrice: number; // 价格，必须
    goodsPresentPrice: number; // 现价，必须
    status: GoodsStatusEnum; // 购物车商品状态，必须
    activityPrice: number; // 活动价，必须
    countDown: number; // 活动倒计时，剩余毫秒数，必须
    activityEndTime: string; // 活动结束时间，到期时间，必须
    activityTypes: MarketingActivityTypeEnum[]; // 营销活动类型 1-拼团 2-秒杀 3-分销 4-加价购 5-买赠，必须
    goodsMasterId: string; // 商品id，必须
    specificationItemId: string; // 规格id，必须
    purchasingAmount:number; // 限购数量 非必须
    preSaleStartTimestamp: number; // 预售开始时间戳，必须
    isStock: number; // 是否库存限制（ 0 无限制 1 有限制）
    stock:number; // 库存数量
    deliveryMethod:number; // 配送方式 0 无需  1 需要
}

/**
 * 购物车DTO
 */
export interface ShoppingCartDTO {
    orgId: string; // 组织id，必须
    orgName: string; // 组织名称，必须
    goodsList: CartGoodsItemDTO[]; // 商品列表，必须
}

/**
 * 购物车组织计算数据传输对象
 */
export interface CartOrgCalcDTO {
    /**
     * 组织ID
     */
    orgId: string; // 必须，组织ID

    /**
     * 是否满足了加价购活动门槛
     */
    userCanParticipateBuyAtMarkup: boolean; // 必须，是否满足了加价购活动门槛

    /**
     * 加价购活动ID
     */
    buyAtMarkupId: string; // 加价购活动ID (必须)

    /**
     * 加价购门槛
     */
    useThreshold: number; // 加价购门槛 (必须)

    /**
     * 还差多少钱满足加价购活动门槛
     */
    useGapAmount: number; // 还差多少钱满足加价购活动门槛 (必须)
  }

/**
 * 计算购物车价格信息DTO
 */
export interface CalcCartPriceInfoDTO {
    count: number; // 已选件数，必须
    couponDiscountAmount: number; // 优惠券优惠金额，必须
    hasCouponDiscount: boolean; // 是否有优惠券优惠，必须
    hasSeckillDiscount: boolean; // 是否有秒杀优惠，必须
    seckillDiscountAmount: number; // 秒杀优惠金额，必须
    totalAmount: number; // 商品总金额，必须
    totalAmountAfterDiscount: number; // （优惠后）合计金额，必须
    totalDiscountAmount:number; // 总计优惠金额 必须
    orders:CartOrgCalcDTO[];
}

/**
 * 校验购物车商品列表 interface
 */
export interface ICheckCartGoodsList {
    cartId?: string; // 用户购物车 id，非必须
    goodsMasterId: string; // 商品主键 id，必须
    specificationItemId?: string; // 子规格 id，多规格商品必传，非必须
    quantity: number; // 数量，必须
    activityTypes?: MarketingActivityTypeEnum[]; // 营销活动类型 1-拼团 2-秒杀 3-分销 4-加价购 5-买赠，必须
}

/**
 * 校验购物车商品列表结算能力 -参数
 */
export interface FetchCheckCartGoodsListSettlementAbleParams {
    orders:{
        orgId:string,
        goodsList:ICheckCartGoodsList
    } []
}

/**
 * 购物车是否开启
 */
export interface IsShowCart {

    /**
     * 是否开启购物车
     */
    isShowCart: number;

    /**
     * 是否开启直播
     */
    getIsShowAppletLive?: number;
}
