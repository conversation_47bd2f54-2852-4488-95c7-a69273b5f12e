import { useUserStore } from '../../mobile/stores/user.store'

interface JumpQuestionBankType {
  doStatus: number,
  examMode?: number,
  query: any
}

export const useThirdJumpQuestionBank = () => {
  const { track } = useTrackEvent()
  const getPath = (doStatus: number, examMode?: number) => {
    if (doStatus === 2) {
      track({
        category: '商品详情',
        action: '网课试卷-报告',
      })
      return examMode === 3 ? '/question-bank/estimating/report' : '/question-bank/report'
    } else {
      track({
        category: '商品详情',
        action: '网课试卷-答题',
      })
      return '/question-bank/paper/detail'
    }
  }

  const handleJumpQuestionBank = ({
    doStatus,
    examMode,
    query
  }: JumpQuestionBankType) => {
    const { isXcx } = useAppConfig()
    const userStore = useUserStore()
    const path = getPath(doStatus, examMode)
    if (isXcx) {
      const url = `${path}?${new URLSearchParams(query).toString()}`
      userStore.jumpToQuestionBank(url)
    } else {
      navigateTo({
        path,
        query
      })
    }
  }

  return {
    handleJumpQuestionBank
  }
}
