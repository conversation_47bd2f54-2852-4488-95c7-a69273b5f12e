/**
 * 该hooks主要用于处理弹窗的滚动穿透问题
 * @param pageScrollClassName 页面滚动的类名 若无默认则取默认的.layout-default取
 * @returns
 * ！！！当使用layout作为pageScrollClassName时，一定要注意移除样式 带来的污染
 */

export const useScrollPenetrate = (pageScrollClassName?:string) => {
  let pageScrollEl : HTMLElement | null = null
  onMounted(() => {
    const className = pageScrollClassName || 'layout-default'
    pageScrollEl = document.querySelector(`.${className}`)
  })

  onUnmounted(() => {
    removeScrollPenetrateHandle()
  })

  /**
   * 添加滚动穿透处理
   * @returns
   */
  const addScrollPenetrateHandle = () => {
    if (!pageScrollEl) { return false }
    pageScrollEl.style.height = '100vh'
    pageScrollEl.style.overflow = 'hidden'
  }

  /**
   * 移除滚动穿透处理
   * @returns
   */
  const removeScrollPenetrateHandle = () => {
    if (!pageScrollEl) { return false }
    pageScrollEl.style.height = 'auto'
    pageScrollEl.style.overflow = ''
  }

  return {
    addScrollPenetrateHandle,
    removeScrollPenetrateHandle
  }
}
