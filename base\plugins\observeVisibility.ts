import type { DirectiveBinding } from 'vue'

interface VisibilityOptions {
  callback: (isVisible: boolean, entry: IntersectionObserverEntry) => void
  threshold?: number
  root?: Element | null
  rootMargin?: string
}

declare global {
  interface HTMLElement {
    _visibilityObserver?: IntersectionObserver
  }
}

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('observe-visibility', {
    mounted (
      el: HTMLElement,
      binding: DirectiveBinding<VisibilityOptions | ((isVisible: boolean, entry: IntersectionObserverEntry) => void)>
    ) {
      const options: IntersectionObserverInit = {
        threshold: 0.1,
        root: null,
        rootMargin: '0px'
      }

      let callback: VisibilityOptions['callback']

      if (typeof binding.value === 'function') {
        callback = binding.value
      } else {
        callback = binding.value.callback
        Object.assign(options, {
          threshold: binding.value.threshold ?? options.threshold,
          root: binding.value.root ?? options.root,
          rootMargin: binding.value.rootMargin ?? options.rootMargin
        })
      }

      if (binding.arg) {
        options.threshold = parseFloat(binding.arg)
      }

      const observer = new IntersectionObserver(([entry]) => {
        callback(entry.isIntersecting, entry)
      }, options)

      el._visibilityObserver = observer
      observer.observe(el)
    },
    unmounted (el: HTMLElement & { _visibilityObserver?: IntersectionObserver }) {
      el._visibilityObserver?.disconnect()
    }
  })
})
