import { useUserStore } from '~/stores/user.store'

interface GoodsType {
  cateIdNames: string;
  cateIds: string;
  categoryLabels: string;
  goodsMasterId: string;
  goodsPresentPrice: string;
  goodsTitle: string;
}

interface RecordsType {
  orderSn: string;
  orderStatus: number;
  receivablePrice: string;
  goodsInfos: GoodsType[];
}

export const useAddQuestionBankOrderBehaviorRecords = () => {
  const { appId, isXcx } = useAppConfig()
  const userStore = useUserStore()

  // 判断客户端类型
  function getPlatform () {
    if (isXcx) {
      return 5
    }
    const { isApp, isAndroid, isPhone, isTablet, isPc, isHarmonyOS } = os || {}
    if (isApp && isHarmonyOS) {
      return 11
    }
    if (isApp && isAndroid) {
      return 3
    }
    if (isApp && (isPhone || isTablet)) {
      return 4
    }
    if (isPc) {
      return 1
    }
    return 7
  }

  async function addQuestionBankOrderBehaviorRecordsFn (records: RecordsType) {
    try {
      const isPc = getPlatform() === 1
      // 提交行为记录
      await fetchAddOrdersBehaviorRecord(ActionEnum.CreateOrder, isPc ? SourceEnum.Pc : SourceEnum.Wap, {
        userMobile: userStore.getUserInfo?.mobile || '', // 用户手机号
        productId: appId, // 产品线
        isPromote: 0,
        clientType: isXcx ? '5' : getPlatform(), // 客户端类型
        records // 商品信息
      })
    } catch (error) {
      console.error('上传记录错误', error)
    }
  }

  return {
    addQuestionBankOrderBehaviorRecordsFn
  }
}
