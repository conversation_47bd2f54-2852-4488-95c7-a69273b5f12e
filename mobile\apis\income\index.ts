import type { WithdrawalParams } from './types'

enum Income {
  withdrawalDeposit = '/kukecoupon/wap/marketingGoods/withdrawalDeposit',
  myIncome = '/kukecoupon/wap/distribution/myIncome',
  myIncomeLog = '/kukecoupon/wap/distribution/myIncomeList',
  myWithdrawalLog = '/kukecoupon/wap/distribution/WithdrawalRecordList'
}
// 提现
export async function withdrawalDeposit (body:WithdrawalParams) {
  return useHttp<any>(Income.withdrawalDeposit, {
    method: 'post',
    body,
    watch: false,
    transform: res => res.data,
  })
}

// 我的收益
export async function getMyIncome (body:{id?:string, withdrawalActivityType?:number}) {
  return useHttp<any>(Income.myIncome, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 我的收益记录
export async function getMyIncomeLog (body:{id?:string, page:number, pageSize:number}) {
  return useHttp<any>(Income.myIncomeLog, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 我的提现记录
export async function getMyWithdrawalLog (body:{page:number, pageSize:number, withdrawalActivityType:number}) {
  return useHttp<any>(Income.myWithdrawalLog, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}
