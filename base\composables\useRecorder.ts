import Recorder from 'recorder-core'
// 必须引入的核心

// 引入mp3格式支持文件；如果需要多个格式支持，把这些格式的编码引擎js文件放到后面统统引入进来即可
import 'recorder-core/src/engine/mp3'
import 'recorder-core/src/engine/mp3-engine'
export const useRecorder = () => {
  const ossInstance = ref()
  const { commonApi } = useApi()

  // 上传音频
  const uploadHttp = async (file: any, isInPc: boolean) => {
    console.log('file11', file)
    if (file.size > 20 * 1024 * 1024) {
      Message('文件大小超过20M，请重新上传')
      return
    }
    // 实例化oss
    const { data } = isInPc ? await fetchOSSConfig() : await commonApi.fetchOSSConfig()
    ossInstance.value = initOSS(data || {})
    const result = await unref(ossInstance).put(
      generateFilename(file, 'img/kukecloud/pc/answer/'),
      file
    )
    const { url } = result
    // 将图片上传到阿里云OSS并返回上传结果给客户端
    console.log(url, 'url结果展示----------------------')
    return url
  }

  // 错误处理
  const handleError = (message: string, error?: any) => {
    console.log(message, error)
    return {
      type: false,
      msg: error ? `${message}: ${error}` : message,
    }
  }

  // 校验录音是否支持
  const checkRecordingSupport = (): { type: boolean; msg: string } => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
      return handleError('该设备暂不支持录音')
    }

    // if (isInPc) {
    //   return handleError('为保证音频的音质，录制音频过程中请您佩戴耳机进行录制哦～')
    // }
    return { type: true, msg: 'success' }
  }

  const getAuthOpen = () => {
    try {
      return new Promise((resolve) => {
        window.rec.open(() => {
          console.log('录音已打开')
          resolve(checkRecordingSupport())
        }, (msg: string, isUserNotAllow: boolean) => {
          console.log('无法录音 then:', msg)
          if (isUserNotAllow) {
            Message('您拒绝了麦克风权限，请允许以继续')
          }
          resolve(handleError(`${isUserNotAllow ? 'UserNotAllow，' : ''}无法录音:${msg}`, isUserNotAllow))
        })
      })
    } catch (error) {
      console.log('无法录音 catch:', error)
      return Promise.resolve(handleError('无法录音 catch', error))
    }
  }

  onMounted(() => {
    if (!window.rec) {
      console.log('录音机初始化')
      window.rec = Recorder({
        type: 'mp3', // 录音格式，可以换成wav等其他格式
        sampleRate: 16000, // 录音的采样率，越大细节越丰富越细腻
        bitRate: 16, // 录音的比特率，越大音质越好
        onProcess: (buffers: any) => {
          console.log('buffers: ', buffers)
        }
      })
    }
  })

  return {
    uploadHttp,
    getAuthOpen
  }
}
