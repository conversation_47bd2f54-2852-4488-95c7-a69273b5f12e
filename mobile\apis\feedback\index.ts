import type {
  ListVO,
  ReplyDTO,
  CategoryDTO,
  QuestionDTO,
} from './types'

enum FeedBack {
  questionType = '/kukemarketing/kmQuestionType/selectListProt',
  categoryLabelList = '/kukebasesystem/wap/ksCategory/queryCategoryDetail',
  categoryList = '/kukebasesystem/wap/ksProduct/getClientCategory',
  // 意见反馈
  addUserFeedback = '/kukemarketing/wap/feedback/addProt',
  userFeedbackList = '/kukemarketing/wap/feedback/listProt',
  userFeedbackDetail = '/kukemarketing/wap/feedback/detailProt',
  userFeedbackReply = '/kukemarketing/wap/feedback/replyProt',
  isNewUserReply = '/kukemarketing/wap/feedback/hasNewReplyProt',
  // 课程反馈
  addCourseFeedback = '/kukecoregoods/wap/goodsFeedback/addProt',
  courseFeedbackList = '/kukecoregoods/wap/goodsFeedback/listProt',
  courseFeedbackDetail = '/kukecoregoods/wap/goodsFeedback/detailProt',
  courseFeedbackReply = '/kukecoregoods/wap/goodsFeedback/replyProt',
  isNewCourseReply = '/kukecoregoods/wap/goodsFeedback/hasNewReplyProt',

}

// 获取专业分类
export async function getCategoryList () {
  return useHttp<any>(FeedBack.categoryList, {
    method: 'post',
    transform: data => data.data
  })
}

// 获取专业分类及下级标签
export async function getCategoryLabelList (body: CategoryDTO) {
  return useHttp<any>(FeedBack.categoryLabelList, {
    method: 'post',
    body,
    transform: data => data.data
  })
}

// 反馈问题类型
export async function questionTypeList () {
  return useHttp<any>(FeedBack.questionType, {
    method: 'post',
    transform: data => data.data
  })
}

// 意见反馈添加
export async function addUserFeedback (body: QuestionDTO) {
  return useHttp<any>(FeedBack.addUserFeedback, {
    method: 'post',
    body,
    transform: data => data.data
  })
}

// 意见反馈列表
export async function userFeedbackList (body: ListVO) {
  return useHttp<any>(FeedBack.userFeedbackList, {
    method: 'post',
    body,
    transform: data => data.data,
    isShowLoading: true
  })
}

// 意见反馈详情
export async function userFeedbackDetail (body: CategoryDTO) {
  return useHttp<any>(FeedBack.userFeedbackDetail, {
    method: 'post',
    body,
    transform: data => data.data
  })
}

// 意见反馈回复
export async function userFeedbackReply (body: ReplyDTO) {
  return useHttp<any>(FeedBack.userFeedbackReply, {
    method: 'post',
    body,
    transform: data => data.data
  })
}

// 意见反馈是否有新回复
export async function isNewUserReply () {
  return useHttp<any>(FeedBack.isNewUserReply, {
    method: 'post',
    transform: data => data.data
  })
}

// 课程反馈添加
export async function addCourseFeedback (body: QuestionDTO) {
  return useHttp<any>(FeedBack.addCourseFeedback, {
    method: 'post',
    body,
    transform: data => data.data
  })
}

// 课程反馈列表
export async function courseFeedbackList (body: ListVO) {
  return useHttp<any>(FeedBack.courseFeedbackList, {
    method: 'post',
    body,
    transform: data => data.data,
    isShowLoading: true
  })
}

// 课程反馈详情
export async function courseFeedbackDetail (body: CategoryDTO) {
  return useHttp<any>(FeedBack.courseFeedbackDetail, {
    method: 'post',
    body,
    transform: data => data.data
  })
}

// 课程反馈回复
export async function courseFeedbackReply (body: ReplyDTO) {
  return useHttp<any>(FeedBack.courseFeedbackReply, {
    method: 'post',
    body,
    transform: data => data.data
  })
}

// 课程反馈是否有新回复
export async function isNewCourseReply () {
  return useHttp<any>(FeedBack.isNewCourseReply, {
    method: 'post',
    transform: data => data.data
  })
}
