const AGENT_CODE = 'agentCode'
const AGENT_M_ID = 'agentMId'
const AGENT_M_TYPE = 'agentMType'

export default defineNuxtRouteMiddleware((to) => {
  // 代理商相关 start
  if (to.query[AGENT_CODE]) {
    useCookie<string>(AGENT_CODE).value = to.query[AGENT_CODE]
    useCookie<string>(AGENT_M_ID).value = to.query[AGENT_M_ID]
    useCookie<string>(AGENT_M_TYPE).value = to.query[AGENT_M_TYPE]
  }
  // 代理商相关 end
})
