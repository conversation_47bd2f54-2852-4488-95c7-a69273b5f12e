const zIndex = ref(0)
export const DEFAULT_INDEX = 2000
/**
 * @example
 * ```
 *  const zIndex = useZIndex().nextZIndex()
    const contentStyle = computed(() => {
      return {
        zIndex,
      }
    })
 * ```
 * @returns
 */
export const useZIndex = () => {
  const initialZIndex = ref(DEFAULT_INDEX)
  const currentZIndex = computed(() => initialZIndex.value + zIndex.value)

  const nextZIndex = () => {
    zIndex.value++
    return currentZIndex.value
  }

  return {
    initialZIndex,
    currentZIndex,
    nextZIndex,
  }
}
