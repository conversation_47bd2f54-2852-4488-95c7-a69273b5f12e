/**
 * 助力好友参数
 */
export interface HelpFriendFetchParams{
  id: string; // 活动ID
  inviteUserId: string; // 助力好友ID
}
/**
 * 排行榜列表请求参数
 */
export interface InviteRankingListFetchParams{
  id: string; // 活动ID
  page: number; // 当前页
  pageSize: number; // 当前页条数
}

/**
 * 领取福利请求参数
 */
export interface ReceiveWelfareFetchParams{
  id: string; // 活动ID
  goodsMasterId?: string; // 商品ID
  specificationItemId?:string // 规格id
  couponId?: string; // 优惠券ID
  goodsId: string; // 活动商品ID
}

/**
 * 【C端】用户登记收货信息请求参数
 */
export interface ShippingAddressFetchParams{
  id: string; // 活动ID
  name: string; // 姓名
  mobile:string // 手机号
  address: string; // 地址
}
/**
 * 下拉选择项
 */
export interface Option {
  /** 选项 */
  option?: number;

  /** 选项名称 */
  optionName?: string;
}
/**
 * 填写信息内容
 */
export interface RegisterContent {
  /** 字段类型：
   * 1-文本类型，
   * 2-下拉选择
   */
  fieldType?: number;

  /** 字段名称 */
  fieldName: string;

  /** 字段内容 */
  fieldContent: string;

  /** 字段ID */
  id?: string;

  /** 是否必填：1-是，0-否 */
  isMust?: number;

  /** 下拉选择项 */
  options?: Option[];
}
/**
 * 【C端】回显收货信息
 */
export interface ShippingAddressInfoDTO {
  /** 领取记录ID */
  recordId: string;

  /** 姓名：1-勾选，0-未勾选 */
  name?: string;

  /** 手机号：1-勾选，0-未勾选 */
  mobile?: number | string;

  /** 邮寄地址：1-勾选，0-未勾选 */
  address?: string;

  /** 填写的姓名信息 */
  nameContent?: string;

  /** 填写的手机信息 */
  mobileContent?: string;

  /** 省的id */
  provinceId?: string;

  /** 市的id */
  cityId?: string;

  /** 县的id */
  countyId?: string;

  /** 详细地址 */
  addressDetail?: string;

  /** 地区地址拼接 */
  regionName?: string;

  /** 商品发货状态：
   * 1-待发货，
   * 2-已发货，
   * 3-已完成，
   * 4-取消发货
   */
  deliveryStatus: string;

  /** 商品发货状态描述 */
  deliveryStatusName: string;

  /** 快递单号 */
  courierNumber: string;

  /** 填写信息列表 */
  registerContentList: RegisterContent[];
}

/**
 * 排行榜item实体
 */

export interface ToplistItemEntity {
  ranked: number; // 排名
  userName: string; // 用户昵称
  inviteCount: number; // 邀请人数
  photo: string; // 头像
}

/**
 * 排行榜列表响应数据
 */
export interface InviteRankingListResponseData {
  count:number,
  activeUser:ToplistItemEntity,
  inviteRankingList:ToplistItemEntity[]
}

/**
 * 活动状态信息 DTO
 */
export interface ActivityStatusInfoDTO {
  available: boolean; // true -活动可用  false-活动不可用
  message:string // 活动信息提示
}
