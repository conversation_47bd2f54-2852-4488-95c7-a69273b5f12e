import { inject } from 'vue'

export const getFormDisabled = (bool: boolean) => {
  // 如果form里设置了，使用form的
  const formProps: any = inject('kkcFormProps', {})
  if (formProps && formProps.disabled) {
    return true
  } else {
    return bool
  }
}

// const list = [{ alias: '大河南', areaName: '河南省', children: [{ alias: '郑州', areaName: '郑州市', children: [{ alias: '港区', areaName: '郑州航空港经济综合实验区', children: [], }, { alias: '', areaName: '巩义市', children: [], }], }] }]
// 协议递归函数将list中的areaName 替换成alias
export const recursion = (list: any) => {
  list.forEach((item: any) => {
    item.areaName = item.alias || item.areaName
    if (item.children && item.children.length > 0) {
      recursion(item.children)
    }
  })
  return list
}
