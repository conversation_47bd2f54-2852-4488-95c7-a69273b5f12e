/**
 * 获取我的套餐商品信息
 */ 
export interface getMyClassRoomInfoProtRequestData {
  /** 试听权益id */
  tryListenId: string;
  /** 商品主id */
  goodsMasterId: string;
  /** 权益id,与 试听权益id 二传一，优先使用权益id */
  userBuyUnitGoodsId: string;
  /** 多规格商品id */
  specificationItemId: string;
}

export interface GoodsTypeList {
  /** 商品类型 */
  goodsType: string;
  /** 课时/试卷对应的状态 ,CourseStatusVO */
  statusList: { statusType: string; statusName: string }[];
  /** 商品类型名称 */
  goodsTypeName: string;
  /** 课时/试卷总数 */
  courseNodeCount: number;
}

export interface SubjectList {
  subjectId: string;
  subjectName: string;
  /** GoodsTypeVO */
  goodsTypeList: GoodsTypeList[];
}
/**
 * 获取我的套餐商品信息
 */ 
export interface getMyClassRoomInfoProtResponseData {
  /** */
  expired: boolean;
  /** 是否有答疑服务 1 有 0 无 */
  isFaq: number;
  /** 专业分类ID */
  cateIds: string;
  /** 商品图 */
  goodsImg: string;
  /** 商品类型1网课，2面授3图书4试卷5套餐6多规格7题库模块8合集 */
  goodsType: number;
  /** 班型 */
  classType: string;
  /** 是否所有免费 0否 1是 */
  isAllFree: number;
  /** 商品名称 */
  goodsTitle: string;
  /** 项目名称 */
  cateIdNames: string;
  /** 科目ids ,SubjectVO */
  subjectList: SubjectList[];
  /** 商品id */
  goodsMasterId: string;
  /** 班型名称 */
  classTypeName: string;
  /** 已学进度（已学课时/商品包含的全部课时） */
  learnProgress: string;
  /** 有效期，格式：yyyy.MM.dd HH:mm */
  expirationDate: string;
  /** 听课率（已学时长/商品包含的全部课时时长*100%） */
  attendanceRate: number;
  /** 总试卷数 */
  totalPaterCount: number;
  /** 已学课时数 */
  learnCourseCount: number;
  /** 总课时数 */
  totalCourseCount: number;
  /** 总讲义数 */
  totalHandoutCount: number;
  /** 子规格id */
  specificationItemId: string;
  /** 总题库模块数 */
  questionModuleCount: number;
  /** 总题库模块单品数 */
  questionSingleCount: number;
  /**  */
  totalAudioCount: number;
}


/**
 * 我的班级-分页列表
 */
export interface getMyClassRoomGoodsPageProtRequestData {
  /** 页码 */
  page: number;
  /** 每页数量 */
  pageSize: number;
  /** 商品类型 */
  goodsType: string;
  /** 科目id */
  subjectId: string;
  /** 商品id */
  goodsMasterId: string;
  /** 权益id */
  userBuyUnitGoodsId: string;
  /** 规格id */
  specificationItemId: string;
}

export interface CourseList {
  /** 权益id */
  id?: string;
  /** 课程id */
  goodsId?: string;
  /** 完成状态，0.未完成，1.已完成 */
  complete?: number;
  /** 课程图片 */
  courseImg?: string;
  /** 课程名称 */
  courseName?: string;
  /** 商品类型 1 网课，2 面授 3 图书 4 试卷包 5 套餐 6 多规格 7 题库模块 8 合集 9其他商品 10 音频 11 直播  12 电子书 */
  courseType?: number;
  /** 总和 */
  totalCount?: number;
  /** 已学总和 */
  learnCount?: number;
  /** 学习状态，0：未开始、1.已学习、2.已学完;当为试卷包时：0未开始 1刷题中 2已做完 */
  learnStatus?: number;
  /** 资料包id */
//   资料包id，字段名待定: string;
  /** 是否设置了学服学习计划:  0 没有；1 有 */
  isStudyPlan: number;
  /** 商品主表id */
  goodsMasterId?: string;
  /** 学习状态名称 */
  learnProgress?: string;
  /** 有效期,yyyy.MM.dd HH:mm */
  expirationDate?: string;
  /** 总试卷数 */
  totalPaterCount?: number;
  /** 已做试卷数 */
  learnPaterCount?: number;
  /** 总课时数 */
  totalCourseCount?: number;
  /** 已学课时数 */
  learnCourseCount?: number;
  /** 总课时讲义数 */
  totalHandoutCount?: number;
  /** 已看讲义数 */
  learnHandoutCount?: number;
  /** 课程更新状态 1有 0无 */
  courseUpdateStatus: string;
  /** 上传的讲义总数 */
  handoutRelatedCount?: number;
}
/**
 * 我的班级-分页列表
 */
export interface getMyClassRoomGoodsPageProtResponseData {
  /** 课程列表 */   
  courseList: CourseList[];
  /** 总数 */
  count: number;
}
 

export {}