import { useUserStore } from '~/stores/user.store'
/**
 * 记录营销活动行为，同步至线索中心订单列表中
 * @params 行为分类  行为来源  其他配置信息options
 */
export const useBehaviorRecord = () => {
  const userStore = useUserStore()
  const addBehaviorRecords = (behaviorClassificationUuid: string, behaviorSource: number | string, options: any = {}) => {
    if (!userStore.isLogin || !behaviorClassificationUuid) { return false }
    updateBehaviorRecords(behaviorClassificationUuid, behaviorSource, {
      userMobile: userStore.getUserInfo?.mobile,
      // cateIds: '99', // 99表示未知专业分类
      // resourceTypes: '',
      // goodsMasterId: '',
      // behaviorTime: '',
      // terminalType: '',
      ...options
    })
  }
  return {
    addBehaviorRecords
  }
}
