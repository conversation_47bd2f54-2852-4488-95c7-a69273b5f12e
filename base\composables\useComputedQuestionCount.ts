import { flat } from '../utils/tools'

interface OptionsType {
  moduleType: number,
  quesList: any[]
}

export const useComputedQuestionCount = (options: OptionsType) => {
  const { moduleType, quesList } = options
  let noDoCount = 0
  let doCount = 0

  let doObjectCount = 0
  let rightCount = 0
  let doAllCount = 0

  // 定义需要匹配的 logStatus 值
  const validLogStatuses = [2, 3, 4, 6]

  if (moduleType === 3) {
    const list = flat(quesList.map(item => item.questionList))
    // 章节练习暂存部分参数
    list.forEach((question: any) => {
      // 3 正确 4 错误 6 半对
      if ([3, 4, 6].includes(question.logStatus)) {
        doObjectCount++
        if (question.logStatus === 3) {
          rightCount++
        }
      }

      if (!validLogStatuses.includes(question.logStatus)) {
        noDoCount++
      }
    })

    doAllCount = list.length - noDoCount
  } else if (moduleType === 2 || moduleType === 0) {
    const isChildQuestionCompleted = (childQuestion: any) => {
      if (childQuestion.ptypeValueId !== 7) {
        return validLogStatuses.includes(childQuestion.logStatus)
      }
    }

    const countLogMStatus = (data: any) => {
      let count = 0 // 筛选未做的题目数量

      // 遍历每道题目
      data.forEach((question: any) => {
        // 遍历每道题的所有小题
        question.childQuestions.forEach((subQuestion: any) => {
          // 筛选未做的题目
          if (!validLogStatuses.includes(subQuestion.logStatus)) {
            count++
          }
        })
      })

      return count
    }

    const isChildQuestionCompletedP7 = (data: any) => {
      const { selectMark, selectNum, questionList } = data

      // 如果不是选做题，直接返回所有题目
      if (!selectMark) {
        return questionList
      }

      // 先根据 validLogStatuses 划分已做题和未做题
      // V2 已做题改为，只要存在已做小题即为已做题
      const [childDoQuestions, childUndoQuestions] = questionList.reduce(
        (acc: any, item: any) => {
          const hasDone = item.childQuestions.some((child: any) =>
            validLogStatuses.includes(child.logStatus)
          )
          const allUndone = item.childQuestions.every(
            (child: any) => !validLogStatuses.includes(child.logStatus)
          )

          if (hasDone) {
            acc[0].push(item) // 已做的题目
          } else if (allUndone) {
            acc[1].push(item) // 有未做的题目
          }
          return acc
        },
        [[], []] // 初始为空数组 [已做的, 未做的]
      )

      // 如果已做题数满足选做题数，返回已做题数组
      if (childDoQuestions.length >= selectNum) {
        return childDoQuestions.slice(0, selectNum)
      }

      // 计算需要做的题目数量
      const remainingToSelect = selectNum - childDoQuestions.length

      // 从未做题目中筛选出remainingToSelect个题目
      const remainingUndoQuestions = childUndoQuestions.slice(0, remainingToSelect)
      return [...childDoQuestions, ...remainingUndoQuestions]

      // // 筛选出部分已经做了一些小题的题目
      // const hasStatusChild = childUndoQuestions.filter((item: any) =>
      //   item.childQuestions.some((child: any) => validLogStatuses.includes(child.logStatus))
      // )

      // if (hasStatusChild.length < remainingToSelect) {
      //   // 如果已做部分题目的数量小于剩余需要选做的题目数，补充未做的题目
      //   const hasEmptyChild = childUndoQuestions.filter((item: any) =>
      //     item.childQuestions.every((child: any) => !validLogStatuses.includes(child.logStatus))
      //   )
      //   return [...hasStatusChild, ...hasEmptyChild.slice(0, remainingToSelect - hasStatusChild.length)]
      // } else {
      //   // 直接取已做部分题目的前 remainingToSelect 个
      //   return hasStatusChild.slice(0, remainingToSelect)
      // }
    }

    const calculateNoDoCount = (question: any) => {
      const completedChildQuestions = question.ptypeValueId !== 7
        ? question.questionList.filter(isChildQuestionCompleted)
        : isChildQuestionCompletedP7(question)

      const count = question.ptypeValueId !== 7 ? completedChildQuestions.length : countLogMStatus(completedChildQuestions)

      if (question.selectMark) {
        const cCount = question.selectNum > count ? question.selectNum - count : 0
        // 选做题
        return question.ptypeValueId !== 7 ? cCount : count
      } else {
        // 非选做题
        return question.ptypeValueId !== 7 ? question.itemCount - count : count
      }
    }

    quesList.forEach((item: any) => {
      item.questionTypeList.forEach((question: any) => {
        noDoCount += calculateNoDoCount(question)
      })
    })
  } else {
    const countLogStatus = (item: any) => {
      if (!item.id && !item.questionId) {
      // 没有ID不进行数量计算
        return false
      }
      if (validLogStatuses.includes(item.logStatus)) {
        doCount++
      } else {
        noDoCount++
      }
    }

    const processItems = (items: { id?: number; questionId?: number; logStatus: number; ptypeValueId?: number; childQuestions?: { id?: number; questionId?: number; logStatus: number }[] }[]) => {
      items.forEach((item: { id?: number; questionId?: number; logStatus: number; ptypeValueId?: number; childQuestions?: { id?: number; questionId?: number; logStatus: number }[] }) => {
        if (item.ptypeValueId !== 7) {
          countLogStatus(item)
        } else {
          item.childQuestions?.forEach((cItem: { id?: number; questionId?: number; logStatus: number }) => {
            countLogStatus(cItem)
          })
        }
      })
    }

    processItems(quesList)
  }

  return {
    noDoCount,
    doCount,
    doObjectCount,
    rightCount,
    doAllCount
  }
}
