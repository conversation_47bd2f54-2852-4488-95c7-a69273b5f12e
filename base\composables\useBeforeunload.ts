export const useBeforeunload = (returnValue = '确定关闭吗') => {
  const beforeunloadListener = (e: BeforeUnloadEvent) => {
    e.preventDefault()
    e.returnValue = returnValue
    return returnValue
  }
  const bindBeforeunloadListener = () => {
    window.addEventListener('beforeunload', beforeunloadListener)
  }
  const unBindBeforeunloadListener = () => {
    window.removeEventListener('beforeunload', beforeunloadListener)
  }
  onUnmounted(() => {
    unBindBeforeunloadListener()
  })
  return {
    beforeunloadListener,
    bindBeforeunloadListener,
    unBindBeforeunloadListener,
  }
}
