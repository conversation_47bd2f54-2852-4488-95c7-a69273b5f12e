<template>
  <div class="rich_text_comp">
    <div id="rich-text-shadow-component" v-richText="htmlContent" class="rich-text-shadow">
      <div v-html="htmlContent" />
    </div>

    <!-- pc 图片预览 -->
    <el-image-viewer
      v-if="routeName === 'news-id' && isShowViewer"
      :url-list="urlList"
      :initial-index="curImgIndex"
      :hide-on-click-modal="true"
      @close="
        () => {
          isShowViewer = false
        }
      "
    />

    <!-- m 图片预览 -->
    <template v-if="routeName === 'news-id'">
      <van-image-preview
        v-model:show="isShowPreview"
        class="mobile_preview_wrap"
        :images="urlList"
        :start-position="curImgIndex"
        :close-on-click-image="false"
        :closeable="true"
        :close-icon="newsClose"
        overlay-class="mobile_preview_overlay"
      />
    </template>
  </div>
</template>

<script lang="ts" setup>
import newsClose from '@/assets/news/news-close.png'
const { name: routeName } = useRoute()

const props = defineProps<{
  content: string,
  imgIsBlock?:boolean
  curImgUrl?:string
}>()
const htmlContent = ref('')
const styleStr = `
  <style>
    img {max-width: 100%; height: auto; cursor: pointer; }
    ${props.imgIsBlock ? 'p img {display: block;}' : 'p img {display: inline-block;}'}
    ${routeName === 'news-id' ? 'p img {border-radius: 16px;}' : ''}
  </style>
`
htmlContent.value = props.content + styleStr
// 图片预览列表
const urlList = ref<string[]>([])
// 是否打开图片预览弹窗
const isShowViewer = ref<boolean>(false)

/**
 * @name getAllImgUrls
 * @description 获取所有图片url
 */
const getAllImgUrls = () => {
  const imgs:any = document.getElementById('rich-text-shadow-component')?.querySelectorAll('img')
  if (!imgs?.length) { return false }
  imgs.forEach((img) => {
    urlList.value.push(img.src)
  })
  console.log('🚀 ~ imgs.forEach ~ urlList:', urlList)
}

/**
 * @name 获取当前图片索引
 */
const curImgIndex = computed((): number => {
  if (props.curImgUrl === undefined) { return 0 }
  const index = urlList.value.findIndex((item: string) => item === props.curImgUrl)
  return index
})

const isShowPreview = ref(false)

onMounted(async () => {
  await nextTick()
  getAllImgUrls()
})

defineExpose({
  isShowViewer,
  isShowPreview
})
</script>

<style lang="scss">
.mobile_preview_overlay {
  background: rgba(0, 0, 0, 0.8) !important;
}
.mobile_preview_wrap {
  .van-image-preview__close-icon {
    font-size: .6rem;
  }
}

</style>
