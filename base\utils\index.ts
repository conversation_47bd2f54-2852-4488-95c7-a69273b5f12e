export const SECS_60 = 60
export const MINS_60 = 60 * 60
export const HOURS_24 = 60 * 60 * 24
export const DAY_999 = 60 * 60 * 24 * 999
export function formatDuring (second: number): { days?: number, hours?: number, minutes?: number, seconds?: number } {
  let duration = {}
  const days = Math.floor(second / HOURS_24)
  const hours = Math.floor((second % HOURS_24) / MINS_60)
  const minutes = Math.floor(((second % HOURS_24) % MINS_60) / SECS_60)
  const seconds = Math.floor(((second % HOURS_24) % MINS_60) % SECS_60)
  if (days > 0) { duration = { days, hours, minutes, seconds } } else if (hours > 0) { duration = { hours, minutes, seconds } } else if (minutes > 0) { duration = { minutes, seconds } } else if (seconds > 0) { duration = { seconds } }
  return duration
}

// TODO 方法合并
export function formatTodayWatchDuration (seconds: number) {
  if (seconds <= SECS_60 && seconds > 0) {
    return { count: 1, unit: '分钟' }
  }
  if (seconds <= MINS_60) {
    return { count: (seconds / SECS_60).toFixed(1), unit: '分钟' }
  }
  return { count: (seconds / MINS_60).toFixed(1), unit: '小时' }
  // return { count: seconds, unit: '秒' }
}
// 初始化时间数据和单位
export function formatTimeDuration (seconds: number) {
  if (seconds <= SECS_60 && seconds > 0) {
    return { count: 1, unit: '分钟' }
  }
  if (seconds <= MINS_60) {
    return { count: (seconds / SECS_60).toFixed(1), unit: '分钟' }
  }
  if (seconds <= HOURS_24) {
    return { count: (seconds / MINS_60).toFixed(1), unit: '小时' }
  }
  return { count: (seconds / HOURS_24).toFixed(1), unit: '天' }
  // return { count: seconds, unit: '秒' }
}

export function hexToRgb (hex: string) {
  // 去除#号
  const color = hex?.replace('#', '')

  // 分割成红、绿、蓝三部分的十六进制字符串
  const r = parseInt(color?.substring(0, 2), 16)
  const g = parseInt(color?.substring(2, 4), 16)
  const b = parseInt(color?.substring(4, 6), 16)

  return {
    r,
    g,
    b,
    hex
  }
}

/**
 * 对象转换为URL参数
 * @param obj
 * @returns
 */
export function objectToUrlParams (obj: Record<string, any>): string {
  return Object.keys(obj)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
    .join('&')
}

/**
 * 计算传入时间与当前时间的差异（以秒为单位）
 *
 * @param time 要比较的时间字符串，格式为 "YYYY-MM-DD HH:mm:ss"
 * @returns 返回传入时间与当前时间的差异（秒数）
 */
export function calculateTimeDifference (time: string) :number {
  // 获取当前时间
  const now: Date = new Date()
  // 将传入的时间字符串转换为 Date 对象
  const nowTime: Date = new Date(time.replace(' ', 'T'))
  // 计算时间差（秒数）
  const diffInSeconds: number = Math.ceil((now.getTime() - nowTime.getTime()) / 1000)
  return diffInSeconds
}

/**
 * 获取当前时间，格式为 "YYYY-MM-DD HH:mm:ss"
 *
 * @returns 返回一个字符串，表示当前的时间，格式为 "YYYY-MM-DD HH:mm:ss"
 */
export function getCurrentTime () :string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以加1
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
