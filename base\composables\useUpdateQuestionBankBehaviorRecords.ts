import { useUserStore } from '~/stores/user.store'

interface DoQuestionActionType {
  goodsMasterId?: string;
  goodsTitle?: string;
  behaviorTime?: number; // 行为时长(秒数)
  cateIds?: string; // 专业分类(多个用逗号隔开)
  extension6?: string; // 标签
  extension7?: string; // 做题内容
  resourceTypes?: string; // 商品类型
}

export const useUpdateQuestionBankBehaviorRecords = () => {
  const { appId, isXcx } = useAppConfig()
  const userStore = useUserStore()
  const route = useRoute()

  const studyLevel1 = ref('')
  const resourceTypes = ref('')
  const goodsMasterId = route.query.goodsMasterId as string
  studyLevel1.value = route.query.studyLevel1 as string
  const moduleName = route.query.moduleName as string

  const activityName = route.query.activityName as string
  const activityId = route.query.activityId as string

  const timer = ref()
  const behaviorTime = ref(0)

  const isStudyPlan = computed(() => route.query.from === '2')

  function createTimer () {
    timer.value = setInterval(() => {
      behaviorTime.value++
    }, 1000)
  }

  // 判断客户端类型
  function getPlatform () {
    if (isXcx) {
      return 5
    }
    const { isApp, isAndroid, isPhone, isTablet, isPc, isHarmonyOS } = os || {}
    if (isApp && isHarmonyOS) {
      return 11
    }
    if (isApp && isAndroid) {
      return 3
    }
    if (isApp && (isPhone || isTablet)) {
      return 4
    }
    if (isPc) {
      return 1
    }
    return 7
  }

  async function getGoodsTitle (id?: string) {
    if (id && id !== 'undefined' && id !== 'null' && id !== '0') {
      const { data, error } = await useHttp<any>(ApisCommon.getGoodsTitleByIds, {
        method: 'post',
        body: {
          ids: [id]
        },
        transform: res => res.data,
      })
      if (!error.value) {
        const { goodsTitle, cateIds, goodsType } = data.value[0]
        studyLevel1.value = cateIds
        resourceTypes.value = goodsType

        return goodsTitle
      }
    }

    return ''
  }

  async function updateBehaviorRecordsFn (options: DoQuestionActionType) {
    try {
      // 获取商品标题
      const title = (goodsMasterId || isStudyPlan.value) ? await getGoodsTitle(goodsMasterId) : ''
      // 答题抽奖活动跳转做题页面时，传入的activityId和activityName
      const t = activityId ? activityName : ''
      // 提交行为记录
      updateBehaviorRecords(ActionEnum.Question, SourceEnum.Wap, {
        userMobile: userStore.getUserInfo?.mobile, // 用户手机号
        productId: appId, // 产品线
        clientType: isXcx ? '5' : getPlatform(), // 客户端类型
        goodsMasterId, // 商品ID
        goodsTitle: t || title || (moduleName ? decodeURIComponent(moduleName) : ''), // 商品标题
        behaviorTime: behaviorTime.value, // 行为时长(秒数)
        cateIds: studyLevel1, // 专业分类(多个用逗号隔开)
        resourceTypes, // 商品类型
        ...options // 扩展选项
      })
    } catch (error) {
      console.error('上传记录错误', error)
    }
  }

  onMounted(() => {
    createTimer()
  })

  onUnmounted(() => {
    // 清除计时器
    clearInterval(timer.value)
    timer.value = null
  })

  return {
    behaviorTime,
    getGoodsTitle,
    updateBehaviorRecordsFn
  }
}
