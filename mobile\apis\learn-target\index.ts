export enum ApisLt {
  getLtByUser = '/kukecoreuser/wap/kuLearningTarget/getLearningTarget',
  postLtOfUser = '/kukecoreuser/wap/kuLearningTarget/saveLearningTarget',
  //
  getLtTree = '/kukemarketing/wap/pageMaster/getLearningTargetList',
  getLtById = '/kukemarketing/wap/pageMaster/getTypeByPageMasterId',
  getLtEnable = '/kukemarketing/wap/pageMaster/getClassifyConfig',
}
// 用户学习目标
export async function getUserLearningTarget (headers = {}) {
  return useHttp<ILearnTargetNode[], any[]>(ApisLt.getLtByUser, {
    method: 'post',
    transform: (input) => {
      if (!Array.isArray(input?.data)) {
        return []
      }
      return input?.data.map((v) => {
        const {
          usedId,
          categoryName,
          level,
          pageMasterId,
          hasChildren,
          aliasType,
        } = v
        return {
          usedId,
          categoryName,
          level,
          pageMasterId,
          hasChildren,
          aliasType,
        }
      })
    },
    headers
  })
}

interface IData {
    list: ILearnTargetNode[];
}

export async function getLearningTargetList () {
  return useHttp<IData>(ApisLt.getLtTree, {
    transform: (input) => {
      //
      return input?.data || {}
    },
    default: () => ({
      list: []
    }),
  })
}

export async function saveLearningTarget (body = {}) {
  return useHttp<object>(ApisLt.postLtOfUser, {
    method: 'post',
    body,
  })
}
export async function getLearningTargetById ({ pageMasterId, productSide }) {
  return useHttp<object>(ApisLt.getLtById, {
    method: 'post',
    body: { pageMasterId, productSide },
    key: 'getTypeByPageMasterId',
    transform: (input) => {
      if (!Array.isArray(input?.data)) {
        return []
      }
      return input?.data?.map((v) => {
        const { usedId, categoryName, level, pageMasterId, hasChildren, isValid, aliasType } = v
        return {
          usedId,
          categoryName,
          level,
          pageMasterId,
          hasChildren,
          isValid,
          aliasType,
        }
      })
    },
  })
}

type LtEnableData = {
  classifyConfig:boolean,
  pageMasterId:string
}
type LtEnableRes = {
  classifyConfig:boolean,
  defaultPageMasterIdHome:string
}

export async function getClassifyConfigEnable () {
  return useHttp<LtEnableData, LtEnableRes>(ApisLt.getLtEnable, {
    method: 'post',
    transform: (input) => {
      const { classifyConfig, pageMasterId } = input?.data || {}
      return { classifyConfig, defaultPageMasterIdHome: pageMasterId }
    },
    default: () => ({}),
  })
}
/**
 * 获取考试目标(只返回默认学习目标的ID)
 * @returns
 */
export async function getLearningTargetDefaultId () {
  return useHttp<object>('/kukemarketing/wap/pageMaster/getLearningTargetDefaultId', {
    method: 'post',
    transform: (res: any) => res.data,
  })
}
/**
 * 公共广告曝光次数和打开次数统计
 * @returns
 */
export async function statisticalPublicAdvertExposure (body = {}) {
  return useHttp<object>('/kukemarketing/wap/pageMaster/statisticalPublicAdvertExposure', {
    key: 'statisticalPublicAdvertExposure' + body.id,
    body,
    method: 'post',
    transform: (res: any) => res.data,
  })
}
