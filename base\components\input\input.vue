<template>
  <div
    class="kkc-form-input"
    :class="{
      'input-prepend': $slots.prepend,
      'input-append': $slots.append,
    }"
    :style="{ width }"
  >
    <div v-if="$slots.prepend" class="prepend">
      <slot name="prepend" />
    </div>
    <input
      v-bind="$attrs"
      ref="inputEl"
      autocomplete="off"
      :value="modelValue"
      :type="inputType"
      :class="{
        disabled: disabledOk,
        'kkc-input-control': true,
        'has-prefix': $slots.prefix || prefixIcon,
        [size]: size,
      }"
      :disabled="disabledOk"
      @input="inputHandler"
      @focus="focusHandler"
      @blur="blurHandler"
    >
    <div v-if="$slots.append" class="append">
      <slot name="append" />
    </div>
    <span v-if="$slots.prefix || prefixIcon" class="prefix-icon">
      <i v-if="prefixIcon" :class="[prefixIcon]" />
      <slot name="prefix" />
    </span>
    <div class="suffix-icon">
      <slot name="suffix" />

      <KKCIcon
        v-if="clear && modelValue"
        name="con-shurukuang-qingchu"
        class="icon-close"
        :size="iconSize"
        color="#C5CAD5"
        @click.stop="clearValue"
      />
      <i
        v-if="modelValue && showEye && type === 'password'"
        :class="{ 'icon-eye-close': eyeShow, 'icon-eye': !eyeShow }"
        @click.stop="eyeShow = !eyeShow"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'

const props = withDefaults(
  defineProps<{
    modelValue?: any
    disabled?: boolean
    type?: string
    clear?: boolean
    showEye?: boolean
    prefixIcon?: string
    suffixIcon?: string
    width?: string
    size?: string
    iconSize?: number
  }>(),
  {
    modelValue: '',
    disabled: false,
    type: 'text',
    clear: false,
    showEye: false,
    prefixIcon: '',
    suffixIcon: '',
    width: '',
    size: '',
    iconSize: 32
  }
)

const emits = defineEmits<{
  (e: 'update:modelValue', modelValue: any): void
  (e: 'change', modelValue: any): void
  (e: 'blur', modelValue: any): void
  (e: 'focus', modelValue: any): void
}>()

const inputEl = ref()
const eyeShow = ref(false)
const disabledOk = computed(() => {
  return getFormDisabled(props.disabled)
})
const inputType = computed(() => {
  if (eyeShow.value) {
    return 'text'
  } else {
    return props.type
  }
})
const blurHandler = (e: Event) => {
  emits('blur', e)
}
const focusHandler = (e: Event) => {
  emits('focus', e)
}
const inputHandler = (e: Event) => {
  const { value } = e.target as HTMLInputElement
  emits('update:modelValue', value)
}
const clearValue = () => {
  emits('update:modelValue', '')
  emits('change', '')
}

const focus = () => {
  inputEl.value.focus()
}
const blur = () => {
  inputEl.value.blur()
}

defineExpose({ blur, focus })
</script>
