// TODO
export const getDeviceEnv = (UA:string) => {
  return {
    UA,
    isAndroid: isAndroid(UA),
    isIos: isIos(UA),
    //
    isTablet: isTablet(UA),
    isIpad: isIpad(UA),
    isIphone: isIphone(UA),
    //
    isWindows: isWindows(UA),
    isMacOS: isMacOS(UA),
    isHarmonyOS: isHarmonyOS(UA),
    //
    isChrome: isChrome(UA),
    isSafari: isSafari(UA),
    isIE: isIE(UA),
    isUC: isUC(UA),
    isFirefox: isFirefox(UA),
    isHuawei: isHuawei(UA),
    isQQ: isQQ(UA),
    isAlipay: isAlipay(UA),
    //
    isWeChat: isWeChat(UA),
    isXcx: isWeChatXcx(UA),
    //
    isKukeCloudAppWebview: isKukeCloudAppWebview(UA),
    isWebview: isWebview(UA),
    isWebCrawl: isWebCrawl(UA),
    // cookieEnabled: window.navigator?.cookieEnabled,
  }
}
export type IDevice =ReturnType<typeof getDeviceEnv>

function isAndroid (a: string): boolean {
  return /android/i.test(a)
}
function isIos (a: string): boolean {
  return /iPad|iPhone|iPod/.test(a)
}
//
function isWindows (a: string): boolean {
  return /Windows/.test(a)
}
function isMacOS (a: string): boolean {
  return /Mac OS X/.test(a)
}
function isHarmonyOS (a: string): boolean {
  return /HarmonyOS|HMSCore|OpenHarmony/.test(a)
}
//
function isTablet (a: string): boolean {
  return /(?:Tablet)/i.test(a)
}
function isIpad (a: string): boolean {
  return /iPad/.test(a)
}
function isIphone (a: string): boolean {
  return /iPhone/.test(a)
}
//
function isChrome (a: string): boolean {
  return /Chrome/.test(a)
}
function isSafari (a: string): boolean {
  return /Safari/.test(a) && !isChrome(a)
}
function isIE (a: string): boolean {
  return /msie|trident/i.test(a)
}
function isUC (a: string): boolean {
  return /UCBrowser/.test(a)
}
function isFirefox (a: string): boolean {
  return /Firefox/.test(a)
}
function isHuawei (a: string): boolean {
  return /HuaweiBrowser/.test(a)
}
function isQQ (a: string): boolean {
  return /QQBrowser/.test(a)
}
function isAlipay (a: string): boolean {
  return /Alipay/.test(a)
}
//
function isWeChat (a: string): boolean {
  return /MicroMessenger/i.test(a)
}
function isWeChatXcx (a: string): boolean {
  return /miniprogram/.test(a)
}
function isKukeCloudAppWebview (a: string): boolean {
  return /kukecloudappwebview/.test(a)
}

function isWebview (a: string): boolean {
  return /webview/i.test(a)
}
function isWebCrawl (a: string): boolean {
  return /spider|crawl|bot/i.test(a)
}
