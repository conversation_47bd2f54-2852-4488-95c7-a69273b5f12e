<template>
  <div>
    <div class="iframediv">
      <!-- <iframe src="https://live.polyv.cn/watch/4201907?userid=2241252&ts=1692858996000&sign=74f588b3446289509868175e4e44d444&hasFrame=1 " frameborder="0" allowfullscreen="true" allow="microphone; camera" /> -->
      <iframe
        rel="prefetch"
        :style="iframeStyle"
        :src="decodeInfo?.url"
        frameborder="0"
        allowfullscreen="true"
        allow="microphone; camera"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { decryptData, createDecipher } from '../../utils/video'
import { getLiveUrl, getCCLiveUrlProt, getLiveUrlOfNoLogin, getCCLiveUrlProtOfNoLogin } from '../../apis/video'
import { isWAP } from '../../utils/is'
import { useUserStore } from '~/stores/user.store'

const userStore = useUserStore()
const {
  clientType
} = useAppConfig()
const route = useRoute()
const { id, goodsMasterId, tryListenId, no } = route.query
definePageMeta({
  ignoreLearnTarget: true
})
interface DecodeInfoType {
  url: string
}
// 获取直播平台类型
// 1 保利威  2 cc
const liveType = ref(1)
const liveOrgId = ref('')
const getLivePlatTypeByType = async (params: any) => {
  Loading(true)
  const { type: platType, orgId: platOrgId } = params
  liveType.value = platType
  liveOrgId.value = platOrgId
  if (liveType.value === 1) {
    // eslint-disable-next-line eqeqeq
    if (no == 1 && !userStore.isLogin) {
      getLivePlayUrl(getLiveUrlOfNoLogin)
    } else {
      getLivePlayUrl(getLiveUrl)
    }
  } else {
    // eslint-disable-next-line eqeqeq, no-lonely-if
    if (no == 1 && !userStore.isLogin) {
      getLivePlayUrl(getCCLiveUrlProtOfNoLogin)
    } else {
      getLivePlayUrl(getCCLiveUrlProt)
    }
  }
}

const init = async () => {
  if (os?.isPc) {
    const pcParams = await getLivePlatformByPc(goodsMasterId as string)
    getLivePlatTypeByType(pcParams)
  } else if (isWAP(clientType)) {
    const wapParams = await getLivePlatformByWap(goodsMasterId as string)
    getLivePlatTypeByType(wapParams)
  }
}
// 保利威 cc 直播信息
const decodeInfo = ref<DecodeInfoType>()
const getLivePlayUrl = async (getInfoFunc: any) => {
  const body = {
    nodeId: id as string,
    goodsMasterId: goodsMasterId as string,
    orgId: liveOrgId.value,
    tryListenId,
  }
  // eslint-disable-next-line eqeqeq
  if (no == 1 && !userStore.isLogin) {
    let _id = useCookie('fingerprintId').value || String(Date.now())
    if (_id.length > 29) {
      _id = _id.slice(0, 29)
    }
    body.visitorId = 'visit' + _id
  }
  const { error, data } = await getInfoFunc(body)

  if (!error.value) {
    const { kkAes, kkSdkString } = data?.value.data
    const kkAesDecode = kkAes
    const kkSdkStringDecode = kkSdkString
    const ivLength = 16
    const encryptedData = decryptData(kkAesDecode) as string
    const iv = encryptedData.slice(ivLength)
    const key = encryptedData.slice(0, ivLength)

    const result = createDecipher(kkSdkStringDecode, key, iv)
    decodeInfo.value = JSON.parse(result)
    Loading(false)
    console.log('🚀 ~ file: index.vue:99 ~ result:', decodeInfo.value)
  }
}

const iframeStyle = ref({})
onMounted(() => {
  init()

  iframeStyle.value = isWAP(clientType)
    ? {
        height: window.innerHeight + 'px',
      }
    : {
        height: '860px',
      }
})

useHead({
  bodyAttrs: {
    class: 'live-setting',
  }
})

</script>

<style scoped lang="scss">
.live-setting {
  height: 100%;
  background: #202126;
  overflow: hidden;
  .iframediv {
    width: 100%;
    // height: 100vh;
  }

  .iframediv iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
