<template>
  <div :style="{ width, height }">
    <div id="playerContainer" :style="{ width, height }" :class=" !isPc ? 'xcx-player' : '' " />
  </div>
</template>

<script setup lang="ts">
import md5 from 'md5'
import { useUserStore } from '~/stores/user.store'
const isPc = os?.isPc
const userStore = useUserStore()
const props = withDefaults(defineProps<{
    width?: string
    height?: string
    siteid: string
    vid: string,
    autoStart?: boolean,
    realAutoPlay?: boolean,
}>(), {
  width: '100%',
  height: '100%',
  autoStart: true,
  realAutoPlay: true,
})

const emits = defineEmits<{
  (e: 'on-ended', value: string): void // 播放结束
  (e: 'on-pause', value: string, type?:string): void // 暂停播放
  (e: 'on-play', value: string): void // 开始播放
  (e: 'on-time-update', playTime: number, duration:number): void // 播放时长
  (e: 'on-ready', data:any): void // 加载完成
  (e: 'on-seek', data:any): void // 播放时长
}>()

// 视频实例
const ccPlayer = ref<any>()
const createPlayer = () => {
  const playerContainer = document.getElementById('playerContainer')
  ccPlayer.value = (window as any).createCCH5Player({
    vid: props.vid,
    vc: md5(props.vid),
    siteid: props.siteid,
    autoStart: props.autoStart,
    // realAutoPlay: props.realAutoPlay,
    width: '100%',
    height: '100%',
    parentNode: playerContainer,
    // watchStartTime: props.lastTimePoint,
    closeHistoryTime: 1,
  })
}
// 暂停
const onCcPauseVideo = (): void => {
  if (ccPlayer.value) {
    ccPlayer.value.pause()
  }
}
// 播放
const onCcResumeVideo = (): void => {
  if (ccPlayer.value) {
    ccPlayer.value.play()
  }
}

// 销毁视频实例
const destroyPlayer = () => {
  if (ccPlayer.value) {
    ccPlayer.value.destroy()
    ccPlayer.value = null
  }
}

defineExpose({
  onCcPauseVideo,
  onCcResumeVideo,
  destroyPlayer
})
function setPlayerCallBack () {
  window.on_CCH5player_play = onPlayVideo
  window.on_CCH5player_pause = onPlayPause
  window.onCCH5PlayerLoaded = onCCH5PlayerLoaded
  window.on_CCH5player_ready = onPlayerReady
  window.on_player_timeupdate = onPlayerTimeupdate
  window.on_CCH5player_ended = onPlayerEnded
  window.on_player_seek = onPlayerSeek
  window.on_h5player_error = onPlayError
  // window.on_player_buffering = onPlayerBuffer
}
// const bufferStatus = ref(0)
// function onPlayerBuffer (obj) {
//   console.log('缓冲中', obj)
//   bufferStatus.value = obj.flag
// }

// 视频播放异常
function onPlayError () {
  Message('请使用其他浏览器观看')
}

// 播放
function onPlayVideo () {
  emits('on-play', ccPlayer.value)
}

function onPlayerReady () {
  if (props.autoStart) {
    setTimeout(() => {
      ccPlayer.value.play()
    }, 50)
  }

  emits('on-ready', ccPlayer.value)
}

function onCCH5PlayerLoaded () {
  window.ccH5PlayerJsLoaded = true
}
// 真实播放时间 正常播放
const realPlayTime = ref(0)
// 暂停
function onPlayPause () {
  emits('on-pause', props.vid, '')
}
// 拖动时间
const seekTime = ref(0)
// 拖动前的时间节点
const seekTimePrev = ref(0)
// 拖动后的时间节点
const seekTimeNext = ref(0)
function onPlayerSeek (prevTime:number, currentTime:number) {
  if (currentTime > prevTime) {
    seekTime.value += (currentTime - prevTime)
  }
  seekTimePrev.value = prevTime
  seekTimeNext.value = currentTime
  emits('on-seek', { vid: props.vid, time: seekTime.value })
}

function onPlayerTimeupdate (currentTime:number) {
  const { playedTimes } = ccPlayer.value.getPlayedPausedTimes()
  realPlayTime.value = playedTimes
  emits('on-time-update', currentTime, realPlayTime.value)
  if (currentTime > 600 && !userStore.isLogin) {
    ccPlayer.value.pause()
    emits('on-pause', props.vid)
  }
}
function onPlayerEnded () {
  emits('on-ended', props.vid)
}

function initCCPlayer () {
  ccPlayer.value && ccPlayer.value.pause()
  ccPlayer.value && ccPlayer.value.destroy()
  createPlayer()
  setPlayerCallBack()
}

watch(() => props.vid, () => {
  initCCPlayer()
})

useHead({
  script: [
    {
      src: '//p.bokecc.com/player?newversion=true',
      onload: () => {
        setTimeout(() => {
          initCCPlayer()
        }, 500)
      }
    },
  ]
})

onUnmounted(() => {
  if (ccPlayer.value) {
    localStorage.setItem('polyvPlayerOption', JSON.stringify(ccPlayer.value))
    ccPlayer.value.destroy()
  }
})

</script>
<style lang="scss">
.xcx-player{
  .ccH5ProgressBar{
    left: 128PX!important;
  }
}

</style>
