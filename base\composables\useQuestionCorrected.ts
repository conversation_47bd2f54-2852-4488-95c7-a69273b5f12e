interface OptionsType {
  /**
   *  aiTeacherStateDesc  评分进度 0不展示 1ai评分中 2ai评分完成 3老师批改中 4老师批改完成
   */
  aiTeacherStateDesc: 0 | 1 | 2 | 3 | 4
}

export const useQuestionCorrected = (Options?: OptionsType) => {
  /**
   *  aiTeacherStateDesc  评分进度 0不展示 1ai评分中 2ai评分完成 3老师批改中 4老师批改完成
   */
  const aiTeacherStateDesc = ref<OptionsType['aiTeacherStateDesc']>(Options?.aiTeacherStateDesc || 0)

  /**
   * 是否需要批改
   */
  const isNeedCorrected = computed(() => (Options?: OptionsType) => {
    if (Options) {
      aiTeacherStateDesc.value = Options.aiTeacherStateDesc || 0
    }
    return aiTeacherStateDesc.value !== 0
  })

  /**
   * 显示AI批改评语
   */
  const showAiCorrected = computed(() => (Options?: OptionsType) => {
    if (Options) {
      aiTeacherStateDesc.value = Options.aiTeacherStateDesc || 0
    }

    return aiTeacherStateDesc.value === 2
  })

  /**
   * 显示老师批改评语
   */
  const showTeacherCorrected = computed(() => (Options?: OptionsType) => {
    if (Options) {
      aiTeacherStateDesc.value = Options.aiTeacherStateDesc || 0
    }

    return aiTeacherStateDesc.value === 4
  })

  /**
   * 批改提示语
   */
  const correctedMessage = computed(() => (Options?: OptionsType) => {
    if (Options) {
      aiTeacherStateDesc.value = Options.aiTeacherStateDesc || 0
    }
    if (!isNeedCorrected.value) {
      return ''
    }

    switch (aiTeacherStateDesc.value) {
      case 1:
        return '主观题AI批改中，请稍后再来查看…'
      case 2:
        return '主观题AI批改完毕，分数仅供参考'
      case 3:
        return '主观题老师批改中，请稍后再来查看…'
      case 4:
        return '主观题老师批改完毕'
    }

    return ''
  })

  /**
   * 是否显示已批改标识
   */
  const showCorrected = computed(() => (Options?: OptionsType) => {
    if (Options) {
      aiTeacherStateDesc.value = Options.aiTeacherStateDesc || 0
    }
    if (!isNeedCorrected.value) {
      // 不需要直接返回false
      return false
    }

    return aiTeacherStateDesc.value === 4 || aiTeacherStateDesc.value === 2
  })

  return {
    /**
     * 是否需要批改
     */
    isNeedCorrected,
    /**
     * 批改提示语
     */
    correctedMessage,
    /**
     * 显示已批改标识
     */
    showCorrected,
    /**
     * 显示AI批改评语
     */
    showAiCorrected,
    /**
     * 显示老师批改评语
     */
    showTeacherCorrected,
  }
}
