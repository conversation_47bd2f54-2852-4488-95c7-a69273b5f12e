.dialog-mask {

  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 9px 12px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.7);
  z-index: 10000002;
  font-size: 16px;
  font-weight: 400;
  // white-space: nowrap;
  color: #ffffff;
}
// .width-wap{
//   width: auto;
//   max-width: 54% !important;
// }
.font-size-14 {
  font-size: 14px;
  line-height: 24px;
  max-width: 80%;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 1;
  transform: translate3d(-50%, -100%);
}