import type { CateRes, DetailListRes, DetailPrams, DetailRes, PageListPrams, PageListRes, SecondCateParams, SecondCateRes } from './types'

enum BookApi {
    // 图书勘误列表
    getPageList = '/kukecoregoods/wap/bookErrata/pageList',

    // 获取分类
    fetchCateList = '/kukebasesystem/wap/ksProduct/getClientCategory',

    // 获取二级分类
    fetchSecondCateList = '/kukebasesystem/wap/productCategory/productCategoryLabelValueList',

    // 图书勘误详情列表
    fetchDetailList = '/kukecoregoods/wap/bookErrata/bookErrataList',

    // 图书勘误详情
    fetachDetail = '/kukecoregoods/wap/bookErrata/detail'

}

/**
 * 获取图书勘误列表
 * @returns
 */
export async function fetchPageList (body:PageListPrams) {
  return useHttp<PageListRes>(BookApi.getPageList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true,
  })
}

/**
 * 获取分类
 */
export async function fetchCateList (body: {}) {
  return useHttp<CateRes>(BookApi.fetchCateList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true,
  })
}

// 获取二级分类
export async function fetchSecondCateList (body: SecondCateParams) {
  return useHttp<SecondCateRes>(BookApi.fetchSecondCateList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true,
  })
}

// 获取图书勘误详情列表

export async function fetchDetailList (body: DetailPrams) {
  return useHttp<DetailListRes>(BookApi.fetchDetailList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true,
  })
}

// 获取图书勘误详情
export async function fetchDetail (id: string) {
  return useHttp<DetailRes>(BookApi.fetachDetail, {
    method: 'post',
    body: { id },
    transform: (res: any) => res.data,
    isShowLoading: true,
  })
}
