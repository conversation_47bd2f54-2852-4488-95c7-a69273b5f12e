<template>
  <div class="w-full  flex justify-center items-center kkc-empty-height">
    <div
      class="kkc-empty-config flex justify-center items-center flex-col"
    >
      <img class="kkc-empty-config__img" :src="EmptyConfigImgSrc" alt="empty-config">
      <div class="kkc-empty-config__text text-[#666]">
        暂无数据，请重试~
      </div>
      <div class="flex kkc-empty-config__btns">
        <button
          v-if="$route.path !== '/'"
          class="kkc-empty-config__btn text-[#111]"
          style="background-color: #f2f2f2;"
        >
          <a href="/">返回首页</a>
        </button>
        <button
          class="kkc-empty-config__btn text-[#fff] bg-brand"
          @click="reload"
        >
          刷新页面
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import EmptyConfigImgSrc from '../assets/images/empty/not-config.png'
const reload = () => location.reload()
</script>

  <style lang="scss" scoped>
  .kkc-empty-height{
    min-height:calc(100vh - 138px)
  }
  .kkc-empty-config {
    &__img {
      width: 210px;
    }
    &__btns {
      margin-top: 24px;
      button {
        border-radius: 8px;
        &:nth-of-type(2) {
          margin-left: 12px;
        }
      }
    }
    &__text {
      height: 17px;
      font-size: 14px;
      margin-top: 12px;
    }
    &__btn {
      height: 36px;
      font-size: 14px;
      padding: 0 12px;
    }
  }
  </style>
