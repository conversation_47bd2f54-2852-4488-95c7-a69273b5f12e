# kuke-cloud 项目

## 项目概述
kuke-cloud 是一个基于 Nuxt.js 3 的多平台项目，包含 PC 端和移动端两个独立构建目标。项目采用现代化的前端技术栈，支持 SSR 渲染，提供丰富的功能模块。

## 环境要求
- Node.js >= 18.18
- pnpm >= 7
- 推荐使用 VS Code 作为开发工具

## 安装指南
1. 克隆项目仓库
2. 安装依赖：
   ```bash
   pnpm install
   ```

## 运行项目
- 开发模式：
  ```bash
  pnpm dev:mobile  # 运行移动端
  pnpm dev:pc      # 运行PC端
  ```

- 生产构建：
  ```bash
  pnpm build:mobile  # 构建移动端
  pnpm build:pc      # 构建PC端
  ```

## 项目结构
```
kuke-cloud/
├── base/            # 基础共享代码
│   ├── apis/        # API 接口
│   ├── components/  # 公共组件
│   ├── composables/ # 组合式函数
│   ├── constants/   # 常量定义
│   ├── nuxt.config.ts # 基础配置
│   └── ...
├── mobile/          # 移动端代码
│   ├── app.vue      # 移动端入口
│   ├── nuxt.config.ts # 移动端配置
│   ├── pages/       # 移动端页面
│   └── ...
├── pc/              # PC端代码
│   ├── app.vue      # PC端入口
│   ├── nuxt.config.ts # PC端配置
│   ├── pages/       # PC端页面
│   └── ...
├── build/           # 构建相关脚本
└── package.json     # 项目配置
```

## 核心功能
- 用户系统（登录、注册）
- 购物车和订单管理
- 题库系统
- 学习中心
- 支付系统
- 多种埋点统计（百度、友盟）
- 微信相关功能集成

## 技术栈
- 框架: Nuxt.js 3
- UI 组件库: 
  - 移动端: Vant
  - PC端: Element Plus
- 状态管理: Pinia
- CSS: Tailwind CSS + PostCSS
- 代码规范: ESLint + Prettier
- 提交规范: cz-customizable

## 开发注意事项
1. 组件命名使用 KKC 前缀
2. 环境变量配置在 .env 文件中
3. 代码提交前需通过 ESLint 检查
4. 重要配置修改需在团队内同步

## 部署说明
项目支持多种环境部署：
- dev: 开发环境
- staging: 预发布环境
- pre: 预生产环境
- prod: 生产环境

部署前请确保配置正确的环境变量。

## 常见问题
1. 如遇到依赖安装问题，请尝试删除 node_modules 和 pnpm-lock.yaml 后重新安装
2. 开发时如遇到 HMR 不工作，请检查端口配置
3. 生产构建时请确保环境变量配置正确





