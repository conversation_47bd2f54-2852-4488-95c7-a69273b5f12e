export function useOnline () {
  const { isKukeCloudAppWebview } = useAppConfig()
  const isOnline = ref(true)
  const isWeakNet = ref(false)
  const setIsOnline = (bool:boolean) => {
    isOnline.value = bool
  }
  function handleConnectionChange (_e) {
    console.log('handleConnectionChange', navigator.connection)
    const { effectiveType, downlink } = navigator.connection
    if (effectiveType === '2g' || downlink < 0.5) {
      isWeakNet.value = true
      downlink > 0 && Message('网络开小差了，请刷新页面~')
    } else {
      isWeakNet.value = false
    }
  }
  const handleOnline = () => {
    setIsOnline(true)
  }
  const handelOffline = () => {
    setIsOnline(false)
  }

  onMounted(() => {
    if (!isKukeCloudAppWebview) {
      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handelOffline)
      if (navigator?.connection) {
        // 初始化执行
        handleConnectionChange(navigator.connection)
        navigator.connection.addEventListener('change', handleConnectionChange)
      } else {
        console.warn('浏览器不支持 navigator.connection 属性')
      }
    }
  })
  onBeforeUnmount(() => {
    if (!isKukeCloudAppWebview) {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handelOffline)
      if (navigator?.connection) {
        navigator.connection.removeEventListener('change', handleConnectionChange)
      } else {
        console.warn('浏览器不支持 navigator.connection 属性')
      }
    }
  })
  return {
    isOnline,
    isWeakNet,
  }
}
