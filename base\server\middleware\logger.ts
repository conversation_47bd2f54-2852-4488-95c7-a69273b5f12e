// import { consola } from 'consola'
export default defineEventHandler(async (event) => {
  const { method } = event
  const METHOD = method.toLocaleUpperCase()
  // console.log(`<-------- ${new Date().toISOString()} [${process?.env?.NODE_ENV}] [${METHOD}] ${getRequestURL(event)}`)
  console.log(JSON.stringify({
    reqTime: new Date().toISOString(),
    method: METHOD,
    reqURL: getRequestURL(event),
    NODE_ENV: process?.env?.NODE_ENV,
    headers: getRequestHeaders(event),
    response: {
      headers: getResponseHeaders(event),
      status: getResponseStatus(event),
      // statusText: getResponseStatusText(event),
    }
  }))
  // console.log('-------->')
  // const URL = getRequestURL(event)
  // // 非GET请求 并且 非 ['/api/', '/prod-api/'] 接口
  // if (METHOD !== 'GET' && !URL.pathname.startsWith('/api/')) {
  //   // if (URL.pathname !== '/') {
  //   //   return await sendRedirect(event, '/', 302)
  //   // }
  //   throw createError({
  //     statusCode: 405,
  //   })
  // }
})
