export enum ApisAddress {
  getAddressList = '/kukecoreuser/wap/address/addressListProt',
  postAddress = '/kukecoreuser/wap/address/addAddressProt',
  editAddress = '/kukecoreuser/wap/address/updateAddressProt',
  delAddress = '/kukecoreuser/wap/address/delAddressProt',
  addressDetail = '/kukecoreuser/wap/address/getAddressDetailProt',
  updateDefaultAddress = '/kukecoreuser/wap/address/updateDefaultAddressProt',
  getDefaultAddressProt = '/kukecoreuser/wap/address/getDefaultAddressProt',
  getCurReceivingInfo='/kukeonlineorder/wap/order/getDeliveryReceiverInfoProt',
}
const formatTree = (tree: any[]) => {
  if (!tree) { return [] }
  tree.forEach((node) => {
    if (node.children?.length) {
      formatTree(node.children)
    } else {
      node.children = undefined
    }
  })
  return tree
}

// 我的地址省市区
export async function getAreaList (body = {}) {
  return useHttp<any>('/kukesystem/area/list', {
    body,
    transform: (input: any) => {
      input.data.list = formatTree(input.data?.list)
      return input.data
    },
  })
}

// 用户地址列表
export async function getAddressList () {
  return useHttp<any>(ApisAddress.getAddressList, {
    transform: (res) => {
      return res?.data?.map((v) => {
        if (v.receiveMobile) {
          v.receiveMobile = atob(v.receiveMobile)
        }
        return v
      })
    },
    default () {
      return []
    },
  })
}
// 添加用户地址
export async function postAddress (body: any) {
  return useHttp<any>(ApisAddress.postAddress, {
    body
  })
}
// 更改用户地址
export async function editAddress (body: any) {
  return useHttp<any>(ApisAddress.editAddress, {
    body,
    isShowLoading: false,
  })
}
// 删除用户地址
export async function delAddress (body: any) {
  return useHttp<any>(ApisAddress.delAddress, {
    body
  })
}
// 地址id获取详细信息
export async function getAddressDetail (body: any) {
  return useHttp<any>(ApisAddress.addressDetail, {
    body,
    transform: (res) => {
      const { receiveMobile } = res?.data ?? {}
      if (receiveMobile) {
        res.data.receiveMobile = atob(receiveMobile)
      }
      return res.data
    },
    default () {
      return {}
    },
  })
}
// 设置地址为默认地址
export async function updateDefaultAddress (body: any) {
  return useHttp<any>(ApisAddress.updateDefaultAddress, {
    body
  })
}
/**
 *
 * 获取用户设置的默认收货地址
*/
export async function getDefaultAddressProt (body: any) {
  return useHttp<any>(ApisAddress.getDefaultAddressProt, {
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 查询当前使用的收货信息
export async function getCurReceivingInfo (body: {id: string, orderSn?: string}) {
  return useHttp<any>(ApisAddress.getCurReceivingInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
