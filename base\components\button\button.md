## 基础用法

按钮支持 `default`、`primary`、 `info`、`success`、`warning`、`danger`、`text` 六种类型，默认为 `default`。
**`success`、`warning`、`danger`、`text`  暂时没有用到**

```
    <KKCButton type="primary">主要按钮</KKCButton>
    <KKCButton type="info">信息按钮</KKCButton>

```

## APIS

### Button Props
  ### 

| 参数       | 类型          | 说明                                                         |
| ---------- | ------------- | ------------------------------------------------------------ |
| type       | String        | 类型，实际是给按钮添加类名，支持`default`、`primary` |
| size       | String        | 大小尺寸，支持 `large`、`normal`、`small` 三种尺寸，默认为 `normal` |
| width      | String        | 按钮宽                                                       |
| round      | boolean/false | 是否圆角按钮                                                 |
| plain      | boolean/false | 是否为朴素按钮                                               |
| href       | String        | a 标签链接地址                                               |
| disabled   | boolean/false | 是否禁用状态                                                 |
| icon       | String        | 前缀 icon                                                    |
| loading    | boolean/false | 是否加载中状态                                               |
| nativeType | String        | 原生 type 属性，button / submit / reset                      |
| name       | String        | 按钮组时有效，按钮组点击事件时返回当前按钮唯一标识           |


### Button Event

| 参数  | 说明 |
| ----- | ---- |
| click | -    |