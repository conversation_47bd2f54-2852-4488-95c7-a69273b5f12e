// const NEWS_TAG_MAP:Record<string, string> = {}
import { informationLabelMap } from '../constants/informationLabelMap'
import { CATE_ID_MAP, CITY_TREE_MAP, NEW_CATEGORY_LIST, type NewsCaegoryItem } from '../constants/db-redirect-kuke99'
// const regexCourse = /\/(live|classroom)\/(\d+)\.html/
const regexCourse = /^\/(course|live|classroom|book|exam|offline_course)\/(\d+)(?:\.html)?/
const regexNews = /^\/(zsb|sydw|jszp|jszg|tgjs|gwy|szyf|xmt|kkgw|kknews|cjkj|zjkj)\/(\d+)(?:\.html)?/
const regexNewsTag = /^\/news\/tag\/([0-9a-zA-Z_-]+)/
const regexNewsCategory = /^\/news\/(zsb|sydw|jszp|jszg|tgjs|gwy|szyf|xmt|kkgw|kknews|cjkj|zjkj)\/?(.+)?/
const defaultResult = {
  // TYPE: null,
  // type: null,
  id: null,
  // getPathname () {}
}

type IType = 'course' | 'news' | 'news-tag' | 'news-category';
interface IResult {
  TYPE?: IType | null;
  type?: string ;
  id: string | null;
  // [key: string]: string;
  cateId?: string ;
  newsClassifyId?: string ;
  province?: string ;
  city?: string ;
  area?: string ;
  getPathname?(id:string, type?:string): string ;
}

export function redirectKuke99Url (url: string, isWap:boolean): IResult {
  if (url.endsWith('_wx') || url.includes('_wx_') || url.endsWith('_wxc')) {
    return defaultResult
  }

  const matchCourse = url.match(regexCourse)
  const matchNews = url.match(regexNews)
  const matchNewsTag = url.match(regexNewsTag)
  const matchNewsCategory = url.match(regexNewsCategory)

  if (matchNews) {
    // http://localhost:3006/zsb/606497266292023296.html
    return {
      TYPE: 'news',
      type: matchNews[1],
      id: matchNews[2],
      getPathname (id) {
        return `/news/${id}_wx`
      },
    }
  } else if (matchCourse) {
    // http://localhost:3006/live/606497266292023296.html
    const id = matchCourse[2]
    if (id?.length === 18) {
      return defaultResult
    }
    return {
      TYPE: 'course',
      type: matchCourse[1],
      id,
      getPathname (id, type) {
        return `/course/${id}_wx_${type}`
      },
    }
  } else if (matchNewsTag) {
    // http://localhost:3006/news/tag/zsb
    const id = matchNewsTag[1]
    // 新的资讯标签id长度为18, 匹配成功后返回默认值
    // 迁移过来的id zhaojiaozhenti 最长，为14位
    if (id?.length === 18) {
      return defaultResult
    }
    return {
      TYPE: 'news-tag',
      type: 'news-tag',
      id,
      getPathname (id) {
        const v = informationLabelMap[id]
        if (!v) { return '/' }
        return `/news/tag/${v}`
      },
    }
  } else if (matchNewsCategory) {
    // http://localhost:3006/news/zsb/answer/hn/shangqiu ----> /news/category-list/1?newsClassifyId=122_wx&province=10&city=526
    const cateId = matchNewsCategory[1]
    let newsClassifyId = ''
    let province = ''
    let city = ''
    const ids = (matchNewsCategory[2] || '').split('/')
    const idsLength = ids.length
    // const [newsClassifyId, province, city, area] = ids
    if (idsLength >= 1) {
      newsClassifyId = ids[0]
    }
    if (idsLength === 2) {
      province = ids[1]
    } else if (idsLength === 3) {
      province = ids[1]
      city = ids[2]
    } else if (idsLength === 4) {
      // TODO
      // level3 = ids[1]
      province = ''
      city = ''
    }
    const r = {
      TYPE: 'news-category',
      type: 'news-category',
      id: cateId,
      cateId,
      newsClassifyId,
      province,
      city,
      // area: area || '',
    }
    return {
      ...r,
      getPathname (_) {
        const id1 = CATE_ID_MAP[cateId]
        if (!id1) { return '/' }
        let str = `/news/category-list/${id1}`
        //
        const id2 = findNewsCategoryId(id1, newsClassifyId)?.newsClassifyId || findNewsCategoryId(id1, province)?.newsClassifyId
        if (id2) {
          str += `?newsClassifyId=${id2}`
        } else {
          // id2 = CITY_TREE_MAP[newsClassifyId]
          // if (!id2) {
          //   return str
          // }
          // str += `?province=${id2}&city=`
        }
        // http://127.0.0.1:3000/news/zsb/hb  ----> /news/category-list/1?province=11&city=
        const id3 = CITY_TREE_MAP[newsClassifyId] || CITY_TREE_MAP[province]
        // if (!id4) {
        //   return str
        // } else {
        //   str += `&city=${id4}`
        // }
        if (id3) {
          const id4 = CITY_TREE_MAP[city] || ''
          if (str.includes('?')) {
            str += `&province=${id3}&city=${id4}`
          } else {
            str += `?province=${id3}&city=${id4}`
          }
          if (isWap) {
            str += '&area='
          }
        }
        // let id3 = CITY_TREE_MAP[province]
        // if (!id3) {
        //   // http://localhost:3006/news/zsb/answer/hn ----> /news/category-list/1?newsClassifyId=122_wx&province=10&city=
        //   // http://localhost:3006/news/zsb/hn/answer ----> /news/category-list/1?newsClassifyId=122_wx&province=10&city=
        //   id3 = findNewsCategoryId(id1, province)?.newsClassifyId
        //   if (!id3) {
        //     return str
        //   }
        //   str += `&newsClassifyId=${id3}`
        // } else {
        //   str += `&province=${id3}&city=`
        // }

        // const id5 = CITY_TREE_MAP[area] || area
        // if (!id5) {
        //   return str
        // } else {
        //   str += `&area=${id5}`
        // }
        return str
      },
    }
  } else {
    return defaultResult
  }
}

const findNewsCategoryId = (id:string, id2:string):NewsCaegoryItem | undefined => {
  return NEW_CATEGORY_LIST.find(v => v.pid === id && id2 === v.chars)
}
