// TODO 文件废弃 0820
export default defineNuxtRouteMiddleware((to) => {
  // TODO 确定使用 onMounted 还是迁移到中间件
  // if (process.server) {
  const {
    pageMasterId: lt,
    // formatCodes,
  } = useLearnTargetState()
  if (to.query[LT_SEARCH_KEY]) {
    // TODO
    // if (!validateLearnTargetNodes()) {
    //   learnTargetNodes.value = formatCodes(to.query[LT_SEARCH_KEY] as string)
    // }
    // } else if (validateLearnTargetNodes()) {
  } else if (lt.value) {
    return navigateTo(
      {
        path: to.path,
        query: {
          ...to.query,
          // TODO
          [LT_SEARCH_KEY]: lt.value,
        },
      },
      {
        // 301 代表永久性转移 302 代表暂时性转移
        // redirectCode: 301,
        replace: true,
      },
    )
  }
  // }
})
