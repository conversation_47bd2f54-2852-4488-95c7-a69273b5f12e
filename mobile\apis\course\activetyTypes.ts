/**
 * 分销活动信息
 */
export interface DistributionProperty {
    /**
     * 分销海报
     * - 图片的URL地址
     */
    distributionPic: string;

    /**
     * 邀请二维码开关
     * - 0: 关闭
     * - 1: 打开
     */
    qrPositionTop: number;

    /**
     * 二维码水平位置（距离父容器的左边距离）
     * - 单位：像素
     */
    qrPositionLeft: number;

    /**
     * 二维码垂直位置（距离父容器的顶部距离）
     * - 单位：像素
     */
    namePositionTop: number;

    /**
     * 昵称水平位置（距离父容器的左边距离）
     * - 单位：像素
     */
    namePositionLeft: number;

    /**
     * 昵称字体大小
     * - 单位：像素
     */
    nameFont: number;

    /**
     * 昵称颜色
     * - 颜色值，例如：#FF0000
     */
    nameColor: string;

    /**
     * 二维码边长
     * - 单位：像素
     * - 无文档说明，但为二维码的实际大小
     */
    qrLengthOfSide: number;

    /**
     * 佣金类型
     * - 1: 比例佣金
     * - 2: 固定佣金
     */
    commissionType: number;

    /**
     * 用户佣金
     * - 单位：货币单位
     * - 根据佣金类型的不同，可以是金额或比例
     */
    userCommission: number;

    /**
     * 邀请昵称开关
     * - 0: 关闭
     * - 1: 打开
     */
    inviteName: number;

    /**
     * 邀请二维码开关
     * - 0: 关闭
     * - 1: 打开
     */
    inviteCode: string;

    /**
     * 域名
     * - 例如："https://example.com"
     * - 用于生成活动链接
     */
    domain: string;
}
/**
 * 拼团具体数据
 */
export interface UserItem {
    /**
     * 用户名称
     * - 该字段表示用户的昵称或用户名
     */
    userName: string;

    /**
     * 用户ID
     * - 唯一标识每个用户
     */
    userId: string;

    /**
     * 用户身份
     * - 1: 团长
     * - 2: 团员
     */
    identity: string;

    /**
     * 用户头像地址
     * - 图片的URL地址
     */
    photo: string;
}

/**
 * 拼团活动数据
 */
export interface TeamListItem {
    /**
     * 已拼团人数
     * - 当前团队已经成功拼团的成员数量
     */
    haveBoughtSu: number;

    /**
     * 还余几人购买
     * - 当前拼团活动还剩下多少人可以加入拼团
     */
    residueBoughtSu: number;

    /**
     * 拼团队伍ID
     * - 唯一标识拼团活动的ID
     */
    teamId: string;

    /**
     * 拼团成员
     * - 团队中的所有用户列表
     */
    users: UserItem[];
}

/**
 * 拼团数据
 */
export interface GroupProperty {
    /**
     * 是否为拼团购买
     * - groupBuy: 拼团购买
     * - originBuy: 原价购买
     * - 可选字段
     */
    isGroup?: 'groupBuy' | 'originBuy';

    /**
     * 拼团队伍ID
     * - 可选字段，用于唯一标识某个拼团活动
     */
    teamId?: string;

    /**
     * 拼团团队信息
     * - 包含拼团活动中的各团队数据
     * - 可选字段
     */
    teamList?: TeamListItem[];

    /**
     * 拼团人数
     * - 设置拼团所需的人数
     * - 可选字段
     */
    groupPeopleCount?: number;
}
/**
 * 砍价活动信息
 */
export interface BargainActivityInfo {
    /**
     * 砍价进度
     * 必须，表示用户参与砍价活动的进度，通常以百分比的形式表示
     */
    bargainProgress: number;

    /**
     * 当前砍到了多少钱
     * 必须，表示用户当前已砍掉的金额
     */
    bargainPrice: number;

    /**
     * 用户是否参与了砍价活动
     * 必须，true表示用户参与，false表示未参与
     */
    participatedInActivities: boolean;

    /**
     * 海报设置
     * 必须，包含海报相关设置的对象
     */
    posterSetting: any;

    /**
     * 弹窗文案
     * 必须，表示弹窗中显示的文案内容
     */
    popupRemark: string;

    /**
     * 分享标题
     * 必须，表示分享时显示的标题
     */
    shareTitle: string;

    /**
     * 分享描述
     * 必须，表示分享时显示的描述内容
     * @example "和朋友一起参与砍价，享受更多折扣。"
     */
    shareDescribe: string;

    /**
     * 分享图片url
     * 必须，表示分享时显示的图片URL
     */
    shareImg: string;

    /**
     * 分享海报url
     * 必须，表示分享时使用的海报URL
     */
    sharePosterUrl: string;

    /**
     * 是否开启底价下单
     * 必须，0表示不开启，1表示开启
     */
    floorOrder: number;

    /**
     * 是否允许自己砍价
     * 必须， 0不允许 1允许
     */
    ownParticipation: number;

   /**
     * 已砍至价格
     * 必须，表示用户参与砍价活动已砍价格
     */

    preferentialPrice: any;
     /**
     * 当前优惠了X元 距低价价格
     * 必须，表示用户参与砍价活动当前优惠了X元
     */
    discountAmount: any;
  }
export interface OrderMarketingInfo {
    activityType?: number
    activityId?: string
    buyAtMarkupId?: string
    couponId?: string;
    userCouponId?: string;
    teamId?: string,
    buyGiveId?: string
    groupProperty?: GroupProperty
    buyGiveSelectCoupons?: string[]
}

/**
 * 商品活动数据
 */
export interface ActivityRes {
    /**
     * 是否是拼团、秒杀、分销商品
     * - 1: 是
     * - 0: 否
     */
    isMarketing: number;

    /**
     * 是否参加买赠活动
     * - 1: 是
     * - 0: 否
     */
    isBuyGive: number;

    /**
     * 商品是否参加过分销活动
     * - 1: 是
     * - 0: 否
     */
    isDistributionGoods: number;

    /**
     * 活动标题
     * - 可选字段
     */
    activityTitle?: string;

    /**
     * 营销活动类型
     * - 1: 拼团
     * - 2: 秒杀
     * - 3: 分销
     * - 4: 加价购
     * - 12: 砍价
     * - 可选字段
     */
    activityType?: number;

    /**
     * 活动ID
     * - 可选字段
     */
    activityId?: string;

    /**
     * 活动状态
     * - 1: 未开始
     * - 2: 已开始
     * - 可选字段
     */
    activityStatus?: number;

    /**
     * 活动倒计时（单位：毫秒）
     * - 可选字段
     */
    countDown?: number;

    /**
     * 活动开始时间
     * - 可选字段，格式为：ISO 8601字符串（例如："2025-01-07T00:00:00Z"）
     */
    activityStartTime?: string;

    /**
     * 活动结束时间
     * - 可选字段，格式为：ISO 8601字符串（例如："2025-01-07T23:59:59Z"）
     */
    activityEndTime?: string;

    /**
     * 活动价格
     * - 可选字段
     */
    activityPrice?: string;

    /**
     * 商品ID
     * - 可选字段
     */
    goodsMasterId?: string;

    /**
     * 子规格ID
     * - 可选字段
     */
    goodsSpecificationItemId?: string;

    /**
     * 分销活动信息
     * - 如果商品参与了分销活动，包含此字段
     * - 可选字段
     */
    distributionProperty?: DistributionProperty;

    /**
     * 拼团活动信息
     * - 如果商品参与了拼团活动，包含此字段
     * - 可选字段
     */
    groupProperty?: GroupProperty;

    /**
     * 拼团倒计时状态
     * - 0: 关闭拼团倒计时
     * - 1: 开启拼团倒计时
     * - 可选字段
     */
    teamworkCountdown?: boolean | number;
    /**
     * 砍价活动信息
     * - 可选字段
     */
    bargainProperty?: BargainActivityInfo
}

export interface ActivityData {
    code?: string
    data?: ActivityRes | undefined
}
