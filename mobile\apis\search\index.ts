import {
  SearchParams
} from './types'

enum SearchApi {
  search = '/kukecoregoods/wap/kgNetworkSchoolGoodsInformation/search',
  courseRecommend = '/kukecoregoods/wap/goods/courseRecommend',
  fetchRecommendByCourseId = '/kukecoregoods/wap/goods/courseRecommendFor404',
  getWxHotSearch = '/kukecoregoods/wap/hotSearch/getWxHotSearch'
}
// 搜索
export async function getSearchList (body: SearchParams) {
  return useHttp<any>(SearchApi.search, {
    method: 'post',
    body,
    isShowLoading: true
  })
}
// 好课推荐
export async function getCourseRecommend (body: any) {
  return useHttp<any>(SearchApi.courseRecommend, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
// （兼容404 页面下）好课推荐【新】
export async function fetchRecommendByCourseId (body: any) {
  return useHttp<any>(SearchApi.fetchRecommendByCourseId, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
// 热门搜索
export async function getWxHotSearch (body: any) {
  return useHttp<any>(SearchApi.getWxHotSearch, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
