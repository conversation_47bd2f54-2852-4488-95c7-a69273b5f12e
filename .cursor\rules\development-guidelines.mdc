---
description: 
globs: 
alwaysApply: false
---
# 开发规范指南

## 代码规范
<!-- - 使用 ESLint 进行代码检查，配置文件：[.eslintrc.js](mdc:.eslintrc.js) -->
- 使用 Prettier 进行代码格式化
- 遵循 Vue 官方风格指南

## Git 提交规范
- 使用 commitizen 进行规范化提交
- 提交规范配置：[.cz-config.js](mdc:.cz-config.js)
- 提交前会自动运行 husky 钩子进行代码检查

## 项目依赖管理
- 使用 pnpm 作为包管理器
- 工作空间配置：[pnpm-workspace.yaml](mdc:pnpm-workspace.yaml)
- 依赖锁定文件：[pnpm-lock.yaml](mdc:pnpm-lock.yaml)

## 开发流程
1. 从主分支创建功能分支
2. 开发完成后提交代码
3. 创建 Pull Request
4. 代码审查通过后合并到主分支

## 注意事项
<!-- - 确保代码通过所有 lint 检查 -->
- 提交前运行测试确保功能正常
- 保持代码简洁，遵循 DRY 原则
- 及时更新文档
