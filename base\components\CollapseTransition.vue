<template>
  <transition name="collapse-transition" v-on="on">
    <slot />
  </transition>
</template>
<script lang="ts" setup>
import type { RendererElement } from 'vue'

const on = {
  beforeEnter (el: RendererElement) {
    console.log(el)
    if (!el.dataset) { el.dataset = {} }

    el.dataset.oldPaddingTop = el.style.paddingTop
    el.dataset.oldPaddingBottom = el.style.paddingBottom

    el.style.maxHeight = 0
    el.style.paddingTop = 0
    el.style.paddingBottom = 0
  },

  enter (el: RendererElement) {
    el.dataset.oldOverflow = el.style.overflow
    if (el.scrollHeight !== 0) {
      el.style.maxHeight = `${el.scrollHeight}px`
      el.style.paddingTop = el.dataset.oldPaddingTop
      el.style.paddingBottom = el.dataset.oldPaddingBottom
    } else {
      el.style.maxHeight = 0
      el.style.paddingTop = el.dataset.oldPaddingTop
      el.style.paddingBottom = el.dataset.oldPaddingBottom
    }

    el.style.overflow = 'hidden'
  },

  afterEnter (el: RendererElement) {
    el.style.maxHeight = ''
    el.style.overflow = el.dataset.oldOverflow
  },

  beforeLeave (el: RendererElement) {
    if (!el.dataset) { el.dataset = {} }
    el.dataset.oldPaddingTop = el.style.paddingTop
    el.dataset.oldPaddingBottom = el.style.paddingBottom
    el.dataset.oldOverflow = el.style.overflow

    el.style.maxHeight = `${el.scrollHeight}px`
    el.style.overflow = 'hidden'
  },

  leave (el: RendererElement) {
    if (el.scrollHeight !== 0) {
      el.style.maxHeight = 0
      el.style.paddingTop = 0
      el.style.paddingBottom = 0
    }
  },

  afterLeave (el: RendererElement) {
    el.style.maxHeight = ''
    el.style.overflow = el.dataset.oldOverflow
    el.style.paddingTop = el.dataset.oldPaddingTop
    el.style.paddingBottom = el.dataset.oldPaddingBottom
  },
}
</script>
<style lang="scss" scoped>
/* .collapse-transition {
  transition: 0.3s width ease-in-out, 0.3s padding-left ease-in-out, 0.3s padding-right ease-in-out;
} */

.collapse-transition {
  transition: 0.3s width ease-in-out, 0.3s height ease-in-out,
    0.3s width ease-in-out, 0.3s padding-top ease-in-out,
    0.3s width ease-in-out, 0.3s padding-bottom ease-in-out;
}

.kkc-collapse-transition-leave-active,
.cck-collapse-transition-enter-active {
  transition: 0.3s width ease-in-out, 0.3s max-height ease-in-out,
    0.3s width ease-in-out, 0.3s padding-top ease-in-out,
    0.3s width ease-in-out, 0.3s padding-bottom ease-in-out;
}
</style>
