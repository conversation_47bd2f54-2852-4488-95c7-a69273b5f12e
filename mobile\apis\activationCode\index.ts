/**
 * 激活码
 * -
 * 活动页详情
 */
export async function activationCodeShare (body: {id:string}) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/activationCodeShare', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 激活码
 * -
 * 验证
 */
export async function activationCodeVerify (body: {id:string}) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/activationCodeVerify', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 激活码
 * -
 * 领取页详情
 */
export async function activationCodeDetail (body: {id:string}) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/activationCodeDetail', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
