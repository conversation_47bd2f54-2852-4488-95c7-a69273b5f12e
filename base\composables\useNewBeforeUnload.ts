import type { RouteLocationNormalized, NavigationGuardNext, Router } from 'vue-router'
import { onUnmounted, ref } from 'vue'
import { showConfirmDialog } from './dialogService'

// 默认的离开提示信息
const DEFAULT_LEAVE_MESSAGE =
  '离开后时间不可暂停，作答时长结束后自动交卷，在结束前可以继续答题'

// 需要应用离开提示的路径列表
const filterPaths = ['/question-bank/paper']

/**
 * 添加路由离开守卫
 * @param router Nuxt Router 实例
 * @param options 包含提示信息、是否应该显示提示的回调和可选的离开前回调的对象
 */
export const useRouteLeaveGuard = (
  router: Router,
  options: {
    message?: string // 可选提示信息
    shouldPrompt: () => boolean // 判断是否需要弹出提示框的回调函数
    beforeLeave?: () => Promise<void> | void // 离开前的回调
  }
) => {
  const { message = DEFAULT_LEAVE_MESSAGE, shouldPrompt, beforeLeave } = options

  const isDialogOpen = ref(false) // 是否正在显示自定义弹窗

  // 处理路由离开
  const handleBeforeRouteLeave = async (
    _to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    console.log(shouldPrompt(), 'shouldPrompt()', isDialogOpen.value)
    if (filterPaths.includes(from.path) && shouldPrompt()) {
      // 弹出确认框
      if (!isDialogOpen.value) {
        isDialogOpen.value = true
        const userConfirmed = await showConfirmDialog(message)
        isDialogOpen.value = false

        if (userConfirmed) {
          next() // 如果用户确认离开，允许路由跳转
        } else {
          next(false) // 如果用户取消离开，阻止路由跳转
        }
      } else {
        next(false) // 如果弹窗已显示，阻止路由跳转
      }
    } else {
      beforeLeave && (await beforeLeave()) // 如果有 beforeLeave 回调，先执行
      next() // 如果不需要提示，直接允许跳转
    }
  }

  const removeGuard = router.beforeEach(async (to, from, next) => {
    await handleBeforeRouteLeave(to, from, next)
  })

  // // 监听浏览器关闭/刷新行为
  // const handleBeforeUnload = async (event: BeforeUnloadEvent) => {
  //   // 调用离开前回调
  //   if (beforeLeave) {
  //     await beforeLeave()
  //   }
  //   event.preventDefault()
  //   return '您确定要离开当前页面吗？'
  // }

  // 监听浏览器返回行为
  const handlePopstate = async () => {
    const currentRoute = router.currentRoute.value

    // 如果有 beforeLeave 回调，先执行
    if (beforeLeave) {
      try {
        await beforeLeave() // 确保离开前的操作完成
      } catch {
        return // 如果回调出错，不继续执行
      }
    }
    // 如果不需要提示，直接返回
    if (!shouldPrompt() || !filterPaths.includes(currentRoute.path)) {
      return
    }

    const userConfirmed = await showConfirmDialog(message)
    if (!userConfirmed) {
      history.pushState(null, '', currentRoute.fullPath) // 阻止返回，保持页面状态
    } else if (window.history.length <= 1) { // 检查是否有上级页面
      navigateTo({ path: '/', replace: true })
    }
  }

  window.addEventListener('popstate', handlePopstate)
  // window.addEventListener('beforeunload', handleBeforeUnload)

  onUnmounted(() => {
    console.log('remove guard')
    removeGuard()
    window.removeEventListener('popstate', handlePopstate)
    // window.removeEventListener('beforeunload', handleBeforeUnload)
  })
}
