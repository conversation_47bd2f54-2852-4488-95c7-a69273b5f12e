/**
 * 渐变色方向
 * vertical 水平方向
 * horizontal 垂直方向
 * crossFromZero 左上角到右下角
 * crossFromBottom 左下角到右上角
 */
export type GradualDirectionType =
  | 'vertical'
  | 'horizontal'
  | 'crossFromZero'
  | 'crossFromBottom';
export type ITheme = 'light'
  | 'lament'
  | 'dark'
  | 'custom'

export interface StyleSetting {
    id?: string;
    wholeStationEssentialColour?: number;
    wholeStationColour?: string;
    wholeStationColourCustom?: string;
    wholeStationDirection?: string;
    wholeStationDirectionCustom?: string;
    wholeStationGradientRamp?: string;
    wholeStationGradientRampCustom?: string;
    marketingMatchColors?: number; // 营销色 切换类型
    marketingColors?: string; // 渐变色的第一个色
    marketingGradientRamp?: string; // 渐变色的第二个色
    marketingDirection?: GradualDirectionType; // 渐变色方向
    marketingColorsCustom?: string; // 自定义颜色
    marketingDirectionCustom?: string; // 自定义颜色方向
    marketingGradientRampCustom?: string; // 自定义颜色的第二个色
}
