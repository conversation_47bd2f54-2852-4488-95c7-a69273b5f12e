export interface TimeDataModel {
  days: number | string;
  hours: string;
  minutes: string;
  seconds: string;
  millisecond: string;
}
export const formatTime = (time: number): TimeDataModel => {
  const SECOND = 1000
  const MINUTE = 60 * SECOND
  const HOUR = 60 * MINUTE
  const DAY = 24 * HOUR

  const format = (time: number) => padTwo(Math.floor(time))
  const day = Math.floor(time / DAY)
  const days = day <= 9 ? `0${day}` : day
  const hours = format((time % DAY) / HOUR)
  const minutes = format((time % HOUR) / MINUTE)
  const seconds = format((time % MINUTE) / SECOND)
  const millisecond = format(time % SECOND)

  return {
    days,
    hours,
    minutes,
    seconds,
    millisecond
  }
}

function padTwo (t: number) {
  return Number(t) >= 10 ? t.toString().slice(0, 2) : '0' + t
}
