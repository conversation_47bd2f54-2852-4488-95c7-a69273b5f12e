import type {
  TargetResultId,
  StartType,
  submitQuestType,
  submitLeaveType,
  submitFinishType,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'

enum Api {
  // 获取试卷状态
  getPaperStatus = '/kukecorequestion/wap/testpaper/testpaperStatus',
  // 查看解析页
  paperMyAnswer = '/kukecorequestion/wap/testpaper/getMyAnswerProt',
  // 开始做题
  paperStart = '/kukecorequestion/wap/testpaper/startProt',
  // 继续做题
  paperDoContinue = '/kukecorequestion/wap/testpaper/doContinueProt',
  // 提交答案
  paperSubmitQuestion = '/kukecorequestion/wap/testpaper/submitQuestionProt',
  // 暂存
  paperSubmitLeave = '/kukecorequestion/wap/testpaper/submitLeaveProt',
  // 提交试卷
  paperSubmitFinish = '/kukecorequestion/wap/testpaper/submitFinishProt',
  // 试卷报告
  paperReport = '/kukecorequestion/wap/testpaper/reportProt',
}

/**
 * 获取试卷状态
 *
 * @param {any} body - 请求参数
 * @return {Promise<any>} - 返回试卷状态数据
 */
export async function getPaperStatus (body: any) {
  return useHttp<any>(Api.getPaperStatus, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 试卷---查看解析页
 *
 * @param {TargetResultId} body - 请求参数
 * @return {Promise<any>} - 返回解析页数据
 */
export async function paperMyAnswer (body: TargetResultId) {
  return useHttp<any>(Api.paperMyAnswer, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 试卷---开始做题
 *
 * @param {StartType} body - 请求参数
 * @return {Promise<any>} - 返回开始做题数据
 * */
export async function paperStart (body: StartType) {
  return useHttp<any>(Api.paperStart, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 试卷---继续做题
 *
 * @param {TargetResultId} body - 请求参数
 * @return {Promise<any>} - 返回继续做题数据
 * */
export async function paperDoContinue (body: TargetResultId) {
  return useHttp<any>(Api.paperDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 试卷---一题一提交
 *
 * @param {submitQuestType} body - 请求参数
 * @return {Promise<any>} - 返回一题一提交数据
 * */
export async function paperSubmitQuestion (body: submitQuestType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(Api.paperSubmitQuestion, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

/**
 * 试卷---中途提交
 *
 * @param {submitLeaveType} body - 请求参数
 * @return {Promise<any>} - 返回中途提交数据
 * */
export async function paperSubmitLeave (body: submitLeaveType) {
  return useHttp<any>(Api.paperSubmitLeave, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false,
  })
}

/**
 * 试卷---完成提交
 *
 * @param {submitFinishType} body - 请求参数
 * @return {Promise<any>} - 返回完成提交数据
 * */
export async function paperSubmitFinish (body: submitFinishType) {
  return useHttp<any>(Api.paperSubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 试卷---查看报告
 *
 * @param {TargetResultId} body - 请求参数
 * @return {Promise<any>} - 返回报告数据
 * */
export async function paperReport (body: TargetResultId) {
  return useHttp<any>(Api.paperReport, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
