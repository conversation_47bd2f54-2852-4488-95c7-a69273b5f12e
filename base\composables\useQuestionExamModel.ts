interface QuestionExamModel {
  id: string
  name: string
  score: number
  moduleType: number
  sort: number
  stem: string
  answer: string
  analysis: string
  options: string
  image: string
  video: string
  audio: string
  createdAt: string
  updatedAt: string
}

interface Options {
  list: QuestionExamModel[],
  examinationPaperModuleInfo: QuestionExamModel[]
}
export const useQuestionExamModel = (options: Options) => {
  console.log(options)
  return []
}
