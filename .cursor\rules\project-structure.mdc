---
description:
globs:
alwaysApply: false
---

<!-- 参考自 https://cursor.directory/nuxtjs-vue-typescript-development-rules -->
你是一位精通 TypeScript、Node.js、NuxtJS、Vue 3、vant、element-plus 和Tailwind的专家。

项目主要目录结构
- `pc/`: PC端项目代码
- `mobile/`: 移动端项目代码
- `base/`: 公共基础代码
- `docs/`: 项目文档
- `build/`: 构建相关配置

项目本地开发运行环境
- 操作系统：windows 10
- 命令行：powershell
- 版本控制：Git
- 依赖管理：pnpm

代码风格与结构
- 编写简洁、技术性的 TypeScript 代码并提供准确示例
- 使用组合式 API 和声明式编程模式；避免选项式 API
- 优先使用迭代和模块化，避免代码重复
- 变量命名需具描述性并包含助动词（如 isLoading, hasError）
- 文件结构：导出组件、组合函数、工具函数、静态内容、类型定义

命名规范
- 目录：小写字母 + 短横线（如 components/auth-wizard）
- 组件：帕斯卡命名法（如 AuthWizard.vue）
- 组合函数：驼峰命名法（如 useAuthState.ts）

TypeScript 使用
- 所有代码必须使用 TypeScript；优先使用 type 而非 interface
- 避免枚举；改用常量对象
- 结合 Vue 3 使用 TypeScript，善用 defineComponent 和 PropType

核心规范
- 使用 Pinia 进行状态管理。
- 优化 Web Vitals 指标（LCP, CLS, FID）
- 利用 Nuxt 的自动导入功能导入`components/`和`composables/`目录下的组件和方法。

Nuxt 特定指南
- 遵循 Nuxt 3 目录结构（如 pages/, components/, composables/）
- 使用 Nuxt 内置功能：
    - 组件/组合函数自动导入
    - pages/ 目录的文件路由系统
    - 通过 Nuxt 插件实现全局功能
- api接口数据获取使用二次封装的 `useHttp`
- 使用 useHead 和 useSeoMeta 实现 SEO 最佳实践

Vue 3 和 Composition API 最佳实践
- 使用 <script setup> 语法简化组件定义
- 利用 ref、reactive 和 computed 管理响应式状态
- 适时通过 provide/inject 实现依赖注入
- 可复用逻辑封装为自定义组合函数

遵循 Nuxt.js 和 Vue.js 官方文档中关于数据获取、渲染和路由的最新最佳实践。