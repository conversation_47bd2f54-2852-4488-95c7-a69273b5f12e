export type FillResult = {
  score: number
  state: number
}

function handleLogAnswer (logAnswer: any) {
  if (typeof logAnswer === 'string') {
    if (logAnswer.includes('[')) {
      return JSON.parse(logAnswer).map((log: { html: string }) => log.html.trim())
    } else {
      return [logAnswer.trim()]
    }
  } else {
    return logAnswer.map((log: { html: string }) => log.html.trim())
  }
}

export const useQuestionBankFillJudge = (item: any) => {
  // 分值
  const score = parseFloat(isNaN(item.score) ? 0 : item.score)
  // 获取输入答案，answer的长度与应该填空数一致
  // const answer = typeof item.logAnswer === 'string' ? [item.logAnswer.trim()] : item.logAnswer.map((log: { html: string }) => log.html.trim())
  const answer = handleLogAnswer(item.logAnswer)
  // 正确答案
  const trueAnswer = item.formattedAnswer
  // 输入答案与正确答案按照后台设置规则对比
  // 判断结果： 3对 4错 6半对
  // awarding填空题多空判分规则：0，顺序一对一  1 顺序不必一致 2 不判分
  const fillResult: FillResult = {
    score: 0,
    state: 1
  }
  // 一共多少个空
  const fillCount = answer.length
  // 是否真实答题，空视为没做
  const isTrueFill = answer.some((v: string) => v)
  // 判断trueAnswer中的每一项是否和answer中的每一项相等
  const resultEveryArr: boolean[] = trueAnswer.map((v: string[], i: number) => {
    console.log('v', Object.prototype.toLocaleString.call(v))
    if (Object.prototype.toLocaleString.call(v) === '[object Object]') {
      return Object.values(v).includes(answer[i])
    } else {
      return v.includes(answer[i])
    }
  })
  // 答对题数
  const resultEveryRightCount = resultEveryArr.reduce((pre, cur) => pre + (cur ? 1 : 0), 0)
  // 判断trueAnswer中的每一项是否和answer中的某一项相等
  const resultSomeArr: boolean[] = answer.map((v: string) => {
    if (v) {
      return trueAnswer.some((item: string[]) => {
        if (Object.prototype.toLocaleString.call(item) === '[object Object]') {
          return Object.values(item).includes(v)
        } else {
          return item.includes(v)
        }
      })
    }

    return false
  })
  // 答对题数
  const resultSomeRightCount = resultSomeArr.reduce((pre, cur) => pre + (cur ? 1 : 0), 0)

  const assignFillEvery = () => {
    // 1，顺序一对一
    if (resultEveryRightCount === fillCount) {
      // 全对
      fillResult.state = 3
      fillResult.score = score
    } else if (resultEveryRightCount < fillCount && resultEveryRightCount > 0) {
      // 部分对了，但是没有全对
      fillResult.state = 6
      fillResult.score = parseFloat((score * (resultEveryRightCount / fillCount)).toFixed(2))
    } else if (isTrueFill) {
      // 全错
      fillResult.state = 4
      fillResult.score = 0
    } else {
      // 未做
      fillResult.state = 1
      fillResult.score = 0
    }
  }

  const assignFillSome = () => {
    // 2 顺序不必一致
    if (resultSomeRightCount === fillCount) {
      // 全对
      fillResult.state = 3
      fillResult.score = score
    } else if (resultSomeRightCount < fillCount && resultSomeRightCount > 0) {
      // 部分对了
      fillResult.state = 6
      fillResult.score = parseFloat((score * (resultSomeRightCount / fillCount)).toFixed(2))
    } else if (isTrueFill) {
      // 全错
      fillResult.state = 4
      fillResult.score = 0
    } else {
      // 未做
      fillResult.state = 1
      fillResult.score = 0
    }
  }
  switch (item.awarding) {
    case 0:
      // 0，顺序一对一
      assignFillEvery()
      break
    case 1:
      // 1 顺序不必一致
      assignFillSome()
      break
    default:
      // 0，顺序一对一
      assignFillEvery()
      break
  }
  return fillResult
}
