import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
const currentDir = dirname(fileURLToPath(import.meta.url))
// alias: {
//   '@': fileURLToPath(new URL('./src', import.meta.url)),
// }
// TODO 使用 module 机制 ？
export default defineNuxtConfig({
  css: [join(currentDir, './theme/index.scss')],
  imports: { dirs: ['./composables', './utils', './types', './apis', './constants'] },
  components: [{ path: './components', prefix: 'KKC' }],
  runtimeConfig: {
    public: {
      TOKEN_KEY: 'TOKEN',
    },
  },
})
