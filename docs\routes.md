## 1. 项目路由命名规范

> 1. 参考第 2 条
> 2. 页面内单独的hook方法需要放到目录`hooks`下
> 3. 页面内单独的组件需要放到目录`components`下
> 4. 除`hooks``components`目录下的`ts``vue`文件会自动挂载到路由，多余的路由会影响系统性能


## 2. 网校 kuke99 路由占用统计

> 即pages目录下不可定义的路由

```
课程详情
/[live|classroom|book|exam|offline_course]/课程ID

资讯详情
/[zsb|sydw|jszp|jszg|tgjs|gwy|szyf|xmt|kkgw|kknews|cjkj|zjkj]/资讯ID

资讯分类
/news/[zsb|sydw|jszp|jszg|tgjs|gwy|szyf|xmt|kkgw|kknews|cjkj|zjkj]/资讯分类ID

```