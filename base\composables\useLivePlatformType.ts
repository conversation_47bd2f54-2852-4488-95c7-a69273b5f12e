import { getLivePort, getLivePortByWap, getLivePortByPc } from '../apis/video'

export const getLivePlatform = async (id:string) => {
  const { data, error } = await getLivePort({
    goodsId: id,
  })
  if (!error.value) {
    const { livePlatform } = data.value.data
    return livePlatform
  }
}
export const getLivePlatformByWap = async (id:string) => {
  const { data, error } = await getLivePortByWap({
    id,
  })
  if (!error.value) {
    const { type, orgId } = data.value.data
    return { type, orgId }
  } else {
    return { type: 1, orgId: '1' }
  }
}
export const getLivePlatformByPc = async (id:string) => {
  const { data, error } = await getLivePortByPc({
    id,
  })
  if (!error.value) {
    const { type, orgId } = data.value.data
    return { type, orgId }
  } else {
    return { type: 1, orgId: '1' }
  }
}
