<template>
  <div class="kkc-overlay" :style="{ backgroundColor, zIndex }" @click.stop="handleClose">
    <slot />
  </div>
</template>

<script setup lang="ts">
defineProps({
  backgroundColor: {
    type: String,
    default: 'rgba(0, 0, 0, 0.3)',
  },
  zIndex: {
    type: Number,
    default: 1111,
  },
})
const emits = defineEmits<{ (type: 'close', e: MouseEvent): void }>()
const handleClose = (e: MouseEvent) => {
  emits('close', e)
}
</script>

<style>
.kkc-overlay {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  /* background-color: rgba(0, 0, 0, 0.3); */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1111;
  /* backdrop-filter: blur(2px); */
}
</style>
