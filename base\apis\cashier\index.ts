// 收银台

export enum ApisCashier {
  /* 通过租户id和产品线id获取租户logo */
  getLogo = '/kukebasesystem/index/product/getProductDetail',
  /* 调起支付 */
  onPay = '/kukeordercenter/checkoutCounter/v1/pay',
  /* 收银台通过订单sn获取商品信息 */
  getOrderDetails = '/kukeordercenter/checkoutCounter/v1/getOrderDetails',
  /* 收银台通过订单sns获取商品信息 购物车 */
  getOrderDetailsByCart = '/kukeordercenter/checkoutCounter/v2/getOrderDetails',
  /* 获取当前的微信appid */
  getWxAppId = '/kukebasesystem/thirdParty/v1/getWxAppId',
  /* 通过授权code 获取openid */
  getOpenId = '/kukecoreuser/wap/woa/oauth/wxOpenid',
  /* 查询订单状态 */
  getOrderPayStatus = '/kukeonlineorder/wap/order/getPayStatus',
  /* 调起支付 购物车 */
  onPayByCart = '/kukeordercenter/checkoutCounter/v2/pay',
}
