/**
 * 生成文件名及存储路径
 * @example
 * file/clue/pc/日期/文件名
 * @param { File } file
 * @param { string } ossPath
 */
export const generateFilename = (file, ossPath = '') => {
  if (typeof ossPath !== 'string') {
    throw new TypeError('类型错误, 请传入字符串')
  }
  let ext = ''
  if (file.name) {
    ext = file.name.split('.')
    ext = '.' + ext[ext.length - 1]
  }
  return (
    ossPath +
    getDayNumber() +
    '/' +
    new Date().getTime() +
    '_' +
    parseInt(Math.random() * 10 ** 4) +
    ext
  )
}

/**
 * 获取但当前日期
 */
function getDayNumber () {
  const d = new Date()
  const yy = d.getFullYear()
  const mm = d.getMonth() + 1
  const dd = d.getDate()
  return `${yy}${mm.toString().padStart(2, '0')}${dd
    .toString()
    .padStart(2, '0')}`
}
export const initOSS = (result: any) => {
  // 获取域名的二级子域名
  const region = result.value.endPoint.split('.')[0]
  console.log('OSS', OSS)
  return new window.OSS({
    region,
    accessKeyId: result.value.accessKeyId,
    accessKeySecret: result.value.accessKeySecret,
    stsToken: result.value.securityToken,
    bucket: result.value.bucketName,
    endPoint: result.value.endPoint,
    expiration: result.value.expiration,
    refreshSTSToken: async () => {
      const expire =
        new Date(result.value.expiration).getTime() - 1 * 60 * 1000
      if (expire > new Date().getTime()) {
        // console.log(
        //   '还未过期：',
        //   '当前时间 ',
        //   new Date(),
        //   '过期时间 ',
        //   result.value.expiration,
        // )
        return {
          accessKeyId: result.value.accessKeyId,
          accessKeySecret: result.value.accessKeySecret,
          stsToken: result.value.securityToken,
        }
      }
      const { data } = await fetchOSSConfig()
      return {
        accessKeyId: data.value?.accessKeyId,
        accessKeySecret: data.value?.accessKeySecret,
        stsToken: data.value?.securityToken,
      }
    },
    refreshSTSTokenInterval: 5 * 60 * 1000,
    success_action_status: '200',
    'x-oss-security-token': result.value.securityToken,
    secure: true,
  })
}
