<!-- eslint-disable no-irregular-whitespace -->
<template>
  <transition name="fade" @after-leave="destroy">
    <div v-show="isVisable" class="dialog-mask" :class="{'font-size-14':isWAP}">
      <img v-if="icon" class="msg-img" :src="icon" alt="">
      {{ message }}
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { isWAP } from '../../utils/is'
const props = defineProps<{
  message: string;
  icon: string;
  duration: number;
  destroy: (el: Element) => void;
}>()

// 控制显示处理
const isVisable = ref(false)

onMounted(() => {
  isVisable.value = true
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  setTimeout(() => {
    isVisable.value = false
  }, props.duration)
})
</script>
<style scoped lang="scss">
.msg-img{
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-bottom: 3px;
}
</style>
