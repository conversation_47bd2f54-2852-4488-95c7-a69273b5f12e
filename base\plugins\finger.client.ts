import Fingerprint2 from '@fingerprintjs/fingerprintjs'

async function getVisitorData () {
  const fp = await Fingerprint2.load()
  return await fp.get()
}

export default defineNuxtPlugin(async () => {
  const { visitorId } = await getVisitorData()
  // nuxtApp.vueApp.config.globalProperties.$clientId = visitorId
  localStorage.setItem('fingerprintId', visitorId)
  const id = useCookie('fingerprintId')
  id.value = visitorId
})
