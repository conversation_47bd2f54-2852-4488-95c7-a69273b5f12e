/**
 * 协议详情请求参数类型
 */
export interface AgreementDetailFetchParams {
  id: string;
}

/**
 * 不含内容的简化协议实体
 */
interface AgreementSimplifyEntity {
  id: string;
  name: string;
}

/**
 * 协议列表响应数据类型
 */
export interface FetchAgreementListResponseData {
  list: AgreementSimplifyEntity[];
}

/**
 * 协议详情响应数据类型
 */
export interface FetchAgreementDetailResponseData {
  content: string; // 协议内容
  name: string;
}
