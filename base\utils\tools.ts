import { Base64 } from 'js-base64'
import { ExamQuestionType, ExciseModeEnum } from '../constants/index'
import { QuestionDoStatusFromLearnStatusName, QuestionDoStatusAlias } from '../constants/questionBankType'

export function flat (arr: any[], depth = 1) {
  return arr.reduce((acc, val) => {
    return Array.isArray(val) && depth > 0
      ? acc.concat(flat(val, depth - 1))
      : acc.concat(val)
  }, [])
}

/**
 * 是否材料题
 * @param type 题型
 * @returns boolean
 */
export function isMaterial (type: number) {
  return [ExamQuestionType.MATERIAL].includes(type)
}

/**
 * 是否填空题
 * @param type 题型
 * @returns boolean
 */
export function isFillQuestion (type: number) {
  return [ExamQuestionType.FILL, ExamQuestionType.SHORT_ANSWER].includes(type)
}

/**
 * 是否多选题
 * @param type 题型
 * @returns boolean
 */
export function isMultipleChoice (type: number) {
  return [ExamQuestionType.MULTIPLE, ExamQuestionType.INDEFINITE].includes(type)
}

/**
 * 是否单选题
 * @param type 题型
 * @returns boolean
 */
export function isRadioChoice (type: number) {
  return [ExamQuestionType.RADIO, ExamQuestionType.JUDGE].includes(type)
}

/**
 * 是否显示解析
 * @param model 做题模式
 * @returns boolean
 * */
export function isShowBankAnalyze (model: number) {
  return model === ExciseModeEnum.EXCISE || model === ExciseModeEnum.QUICK || model === ExciseModeEnum.RECITE
}

/**
 * 是否展开解析
 * @param model 做题模式
 * @param isExpand 是否展开
 * @returns boolean
 * */
export function isOpenBankAnalyze (model: number, isExpand?: boolean) {
  return model === ExciseModeEnum.RECITE || (isShowBankAnalyze(model) && isExpand)
}

/**
 * 题型标题展示
 * @description 试卷类型以及固定刷题、每日一练默认展示展示二级题型，否则展示一级题型；其他类型默认展示一级题型
 *
 * @param first 一级题型
 * @param second 二级题型
 * @param moduleType 类型
 * @returns string 要展示的文案
 */
export function handleQuestionTitle (first: string, second: string, moduleType: number) {
  return [0, 1, 2, 4].includes(moduleType) ? second || first : first || second
}

/**
 * 将字符串转为base64
 * @param str 字符串
 * @returns base64
 */
export function toBase64 (str: string): string {
  return Base64.encode(str)
}

/**
 * 将answer转为base64
 * @param answer 答案
 * @returns base64
 */
export function toAnswerBase64 (answer: any) {
  return answer ? toBase64(typeof answer === 'string' ? answer : JSON.stringify(answer)) : ''
}

/**
 * 根据学习状态名称获取做题状态
 * @param statusName 学习状态名称
 * @returns number
 */
export function getQuestionDoStatusFromLearnStatusName (statusName: string): number {
  if (statusName in QuestionDoStatusAlias) {
    return QuestionDoStatusAlias[statusName as keyof typeof QuestionDoStatusAlias]
  }
  return QuestionDoStatusFromLearnStatusName[statusName as keyof typeof QuestionDoStatusFromLearnStatusName] || 0
}
