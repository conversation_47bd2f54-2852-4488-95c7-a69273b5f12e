// type WxLoginConfig = {
//   appid:string
//   self_redirect:boolean
//   id?:string
//   scope:string
//   redirect_uri:string
//   href:string
//   [key: string]:any
// }
// declare class WxLogin {
//   constructor(config:WxLoginConfig)
// }

// declare module 'nuxt/schema' {
//   interface RuntimeConfig {
//     apiServerHost: string
//     public: {
//       requestAppid: string
//       requestAppkey: string
//       BUILD_ENV: string

//       clientType: string

//       version: string
//       WX_APP_ID: string
//       QQ_APP_ID: string
//       QQ_AUTH_URI: string
//       USER_KEY: string

//       TOKEN_KEY: string
//       LT_LOCAL_KEY: string
//       LT_SEARCH_KEY: string
//     }
//   }
// }

declare global {
  interface Window {
    initAliyunCaptcha: any
  }
}

export {};
