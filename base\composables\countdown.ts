export const countFunction = (time: number, fun: any) => {
  const countDownFn = ref()
  const timeObj = ref()
  if (process.client) {
    // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
    countDownFn.value = setInterval(() => {
      time = time - 1000
      if (time <= 0 && countDownFn.value) {
        timeObj.value = null
        clearInterval(countDownFn.value) // 清除定时器
        fun()
      } else {
        const hours = Math.floor((time / (60 * 60 * 1000)) % 24)
        const minutes = Math.floor((time / (60 * 1000)) % 60)
        const seconds = Math.floor((time / 1000) % 60)
        timeObj.value = {
          hours: hours < 10 ? '0' + hours : hours,
          minutes: minutes < 10 ? '0' + minutes : minutes,
          seconds: seconds < 10 ? '0' + seconds : seconds
        }
        let days: number | string = Math.floor(time / (24 * 60 * 60 * 1000))
        if (days > 0) {
          if (days < 10) {
            days = '0' + days
          }
          timeObj.value.days = days
        }
        let activityDay: number | string = Math.floor(time / (24 * 60 * 60 * 1000))
        if (activityDay >= 0) {
          if (activityDay < 10) {
            activityDay = '0' + activityDay
          }
          timeObj.value.activityDay = activityDay
        }
      }
    }, 1000)
  }
  return {
    timeObj,
    countDownFn
  }
}
