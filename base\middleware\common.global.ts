export default defineNuxtRouteMiddleware((to) => {
  // requireLogin
  // if (!useRequestHeader('kk-token')) {
  //   return navigateTo('/login')
  // }
  const cookie = useCookie<string>(PROMOTION_SHARE_KEY)

  const cookiePoolId = useCookie<string>(POOL_ID)
  const cookieSaleAdminId = useCookie<string>(SALE_ADMIN_ID)
  const cookieTargetCode = useCookie<string>(TARGET_CODE)
  const cookieInternalSystem = useCookie<string>(INNERNAL_SYSTEM)
  const cookieIsAudition = useCookie(IS_AUDITION)
  const cookieSharePrice = useCookie(SHARE_PRICE)
  const cookieShareId = useCookie(SHARE_LINK_ID)
  const cookieExternalUserid = useCookie(EXTERNAL_USER_ID)

  const cookieSystem = useCookie<string>(SYSTEM_TYPE)
  const kkExtension = to.query[PROMOTION_SHARE_KEY]
  // const kkExtensionUrl = to.query[PROMOTION_SHARE_URL]
  const kkSystem = to.query[SYSTEM_TYPE]
  const kkPoolId = to.query[POOL_ID]
  const kkSaleAdminId = to.query[SALE_ADMIN_ID]
  const kkTargetCode = to.query[TARGET_CODE]
  const kkInternalSystem = to.query[INNERNAL_SYSTEM]
  const kkIsAudition = to.query[IS_AUDITION]
  const kkSharePrice = to.query[SHARE_PRICE]
  const kkShareId = to.query[SHARE_LINK_ID]
  const kkExternalUserid = to.query[EXTERNAL_USER_ID]
  if (kkExtension) {
    cookie.value = kkExtension as string
    if (isClient) {
      document.cookie = `${PROMOTION_SHARE_URL}=${location.href}`
    }
  }
  if (kkSystem) {
    cookieSystem.value = kkSystem as string
  }
  if (kkPoolId || kkSaleAdminId) {
    cookiePoolId.value = kkPoolId as string
    cookieSaleAdminId.value = kkSaleAdminId as string
  }
  if (kkTargetCode) {
    cookieTargetCode.value = kkTargetCode as string
  }
  if (kkInternalSystem) {
    if (isClient) {
      const systemId = sessionStorage.getItem(INNERNAL_SYSTEM)
      !systemId && sessionStorage.setItem(INNERNAL_SYSTEM, kkInternalSystem as string)
    }
    cookieInternalSystem.value = kkInternalSystem as string
  }
  if (kkIsAudition) {
    cookieIsAudition.value = kkIsAudition as string
  } else {
    cookieIsAudition.value = undefined
  }
  if (kkSharePrice) {
    cookieSharePrice.value = kkSharePrice as string
  } else {
    cookieSharePrice.value = undefined
  }
  // scrm分享商品
  if (kkShareId && cookieExternalUserid) {
    cookieShareId.value = kkShareId as string
    cookieExternalUserid.value = kkExternalUserid as string
  }
  // const token = getResponseHeader(event, 'kk-token')
  // if (process.server) {
  //   console.log('server', useCookie('lt'))
  // } else if (process.client) {
  //   console.log('process.client', useCookie('lt'))
  // }
})
