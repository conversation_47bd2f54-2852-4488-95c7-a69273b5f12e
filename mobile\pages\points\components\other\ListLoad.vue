<template>
  <van-pull-refresh
    v-model="refreshing"
    success-text="刷新成功"
    @refresh="handleRefresh"
  >
    <van-list
      v-model:loading="loading"
      v-model:error="error"
      :finished="finished"
      :finished-text="finishedText"
      error-text="请求失败，点击重新加载"
      @load="handleLoad"
    >
      <slot />
    </van-list>
  </van-pull-refresh>
</template>

<script setup lang="ts">
// types
import type { Props } from '~/pages/points/types/index'

// 列表加载的返回结果
type LoadResult = {
  bool: boolean
  count: number
  finish?: boolean
}

// 组件定义参数
type ListLoadProps = {
  // 下拉
  pullRefresh: (page: Props) => Promise<LoadResult> | LoadResult
  // 上滑
  upLoad: (page: Props) => Promise<LoadResult> | LoadResult
  // 一页多少条
  pageSize?: number
}

const props = withDefaults(defineProps<ListLoadProps>(), {
  pageSize: 10
})

// 下拉加载 加载过程中的状态
const refreshing = ref<boolean>(false)
// 下拉触发
const handleRefresh = async () => {
  currentPage.value = 1
  loading.value = true
  const page = { page: 1, pageSize: props.pageSize }
  const { bool, finish, count } = await props.pullRefresh(page)
  refreshing.value = !bool

  loading.value = false
  listCount.value = count
  finished.value = !!finish
}

// 当前加载页面
const currentPage = ref<number>(0)
// 列表下滑加载 记载过程中的状态
const loading = ref<boolean>(false)
// 加载异常
const error = ref<boolean>(false)
// 控制数据是否加载完毕
const finished = ref<boolean>(false)
// 列表数据的长度
const listCount = ref<number>(0)
// 没有数据时展示的内容
const finishedText = computed(() => listCount.value ? '没有更多了' : '')
// 下滑加载
const handleLoad = async () => {
  // 加一
  currentPage.value++

  const page = { page: currentPage.value, pageSize: props.pageSize }
  const { bool, finish, count } = await props.upLoad(page)
  if (!bool) {
    currentPage.value--
    error.value = true
  }

  loading.value = false
  listCount.value = count
  finished.value = !!finish
}

// 重置记录的状态
const handleResetLoadStatus = () => {
  refreshing.value = false
  finished.value = false
  loading.value = false
  listCount.value = 0
}
// 重置刷新页面
const handleResetLoad = () => {
  handleResetLoadStatus()
  handleRefresh()
}
defineExpose({ handleResetLoadStatus, handleResetLoad })
</script>
