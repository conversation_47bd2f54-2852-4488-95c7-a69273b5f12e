// 只对滑块的样式有效，单位 px
export interface SlideStyle {
  width: number; // 宽度
  height: number; // 高度
}

// 验证码验证结果
export interface CaptchaVerifyResult {
  captchaResult: boolean; // 验证码验证是否通过
  bizResult?: boolean | undefined; // 业务验证是否通过
}

// 验证码类型
export interface Captcha {
  SceneId?: string; // 验证码场景ID
  prefix?: string; // 验证码身份标
  mode?: 'popup' | 'embed'; // 验证码模式
  element?: string; // 页面上预留渲染验证码的元素
  button?: string; // 触发验证码弹窗的元素
  slideStyle?: SlideStyle; // 滑块验证码样式
  immediate?: boolean; // 嵌入式下，完成验证后，是否立即发送验证请求
  timeout?: number; // 超时事件
  captchaVerifyCallback: (captchaVerifyParam: string) => Promise<CaptchaVerifyResult>,
  onBizResultCallback?: (bizResult: boolean) => void
}

/**
 * @description 阿里云的验证码校验
 *
 * @returns
 */
const useCaptchaVerify = (config?: Captcha) => {
  // 点击验证的按钮
  const captchaButton: any = ref<null>

  // 初始化阿里云验证
  const initAliyunCaptcha = () => {
    // 兜底操作 销毁历史初始化的信息
    destroyAliyunCaptcha()

    // 初始化获取需要操作的元素
    captchaButton.value = document.getElementById(config?.button || 'captcha-button')

    const params = {
      upLang: {
        cn: {
          SLIDE_FAIL: '账号安全验证失败，请刷新页面或联系客服',
          CAPTCHA_FAIL: '账号安全验证失败，请刷新页面或联系客服'
        }
      },
      SceneId: '1beyw8xd', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
      prefix: 'zqkto0', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
      mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
      button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
      slideStyle: {
        width: 360,
        height: 40,
      }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
      language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
      region: 'cn', // 验证码示例所属地区，支持中国内地（cn）、新加坡（sgp）

      ...config, // 自定义
    }

    window.initAliyunCaptcha(params)
  }

  // 销毁阿里云验证
  const destroyAliyunCaptcha = () => {
    captchaButton.value = null

    document.getElementById('aliyunCaptcha-mask')?.remove()
    document.getElementById('aliyunCaptcha-window-popup')?.remove()
  }

  // 处理验证码的状态
  const getVerificationCodeStatus = async (api: Function, params: Record<string, any>) => {
    // 构造标准返回参数
    const verifyResult = { captchaResult: false, bizResult: false }

    // 获取验证码
    const { error } = await api(params)

    // 验证状态吗，更改返回状态
    if (!error.value) {
      // 非异常状态下，正常执行
      verifyResult.bizResult = true
      verifyResult.captchaResult = true
    } else if (error?.value?.data?.code !== '14001') {
      /**
       * 如果非阿里云异常，关闭弹框，但不执行后续操作
       *   业务报错：10001（短信发送失败），17015（短信余量不足，发送失败，请联系客服！）
       *   里云报错：14001（安全校验未通过，验证码发送失败）
       */
      verifyResult.captchaResult = true
    }

    return verifyResult
  }

  return { initAliyunCaptcha, destroyAliyunCaptcha, getVerificationCodeStatus, captchaButton }
}

export default useCaptchaVerify
