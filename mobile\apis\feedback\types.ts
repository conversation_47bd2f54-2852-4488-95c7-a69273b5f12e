export interface CategoryDTO {
  usedId?: string
  id?: string
}

export interface ListVO {
  page: number
  pageSize: number
}

export interface ReplyInfoVO {
  id: string
  feedbackId: string
  userType: number // 用户类型 1小程序用户 2后台用户
  userId: string
  content: string
  isRead: number // 是否已读 0未读 1已读
  isEnd: number // 是否完结 0否 1是
  createdAt: string
  updatedAt: string
  avatar: string
  userName: string
  imgList: string[]
}

export interface FeedBackListVO {
  id: string
  userName: string
  createdAt: string
  questionType: number
  questionTypeName: string
  content: string
  imgList: string[]
  avatar: string
  replyStatus: 0 | 1 | 2 // 回复状态 0无 1后台已回复，用户已查看 2后台新回复，用户未查看
  newReply: number
  cateNames: string
  feedbackGoalsText: string
  tags: string
  goodsTitle?: string
  goodsMasterId?: string
  replyList?: ReplyInfoVO[]
}

export interface FeedBackDetailVO {
  replyList: ReplyInfoVO[]
  detail: FeedBackListVO
}

export interface ReplyInfoDTO {
  content: string
  feedback_id: string
  isEnd?: number
}

export interface QuestionDTO {
  cateId?: string
  regionId?: string
  subjectId?: string
  academicSectionId?: string
  directionId?: string
  imgs?: string[]
  clientType: number,
  questionType?: number
  content: string
  versionNumber?: string
  clientInfo?: string
  goodsMasterId?: string
  courseHourId?: string
  timePoint?: string
}

export interface ReplyDTO {
  feedbackId: string // 反馈id
  content: string // 回复内容
  imgs?: string[]
}
