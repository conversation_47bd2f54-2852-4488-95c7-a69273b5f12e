// 防抖函数封装
export function debounceFunc (func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) { context = args = null }
      }
    }
  }

  return function (...args) {
    // eslint-disable-next-line
      context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) { timeout = setTimeout(later, wait) }
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * 创建一个防抖函数，限制函数的执行频率
 * @param func 需要防抖的函数
 * @param wait 等待时间（毫秒）
 * @param immediate 是否立即执行
 * @returns 防抖处理后的函数
 */
export function debounceFuncPlus<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate: boolean = false
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  let timeout: ReturnType<typeof setTimeout> | null = null
  let params: Parameters<T> | null = null
  let context: any = null
  let timestamp = 0
  let result: ReturnType<T> | undefined

  const later = function (): void {
    // 计算距上一次触发时间的间隔
    const last = Date.now() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, params as Parameters<T>)
        if (!timeout) {
          context = params = null
        }
      }
    }
  }

  return function (this: any, ...args: Parameters<T>): ReturnType<T> | undefined {
    // eslint-disable-next-line
    context = this
    params = args
    timestamp = Date.now()
    const callNow = immediate && !timeout

    // 如果延时不存在，重新设定延时
    if (!timeout) {
      timeout = setTimeout(later, wait)
    }

    if (callNow) {
      result = func.apply(context, params)
      context = params = null
    }

    return result
  }
}
