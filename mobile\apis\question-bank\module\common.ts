import type { SearchListParamsType } from './common.type'

enum Api {
  // 试卷搜索
  searchPaper = '/kukecorequestion/wap/moduleManageTags/searchTestPaperList',
  // 试题搜索
  searchQuestion = '/kukecorequestion/wap/moduleManageTags/searchQuestionList',
  // 获取金刚区数据
  getFixedModuleManage = '/kukecorequestion/wap/moduleManageTags/getFixedModuleInfoList',
}

/**
 * 试卷搜索
 *
 * @param {SearchListParamsType} body - 请求参数
 */
export async function searchPaper (body: SearchListParamsType) {
  return useHttp<any>(Api.searchPaper, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 试题搜索
 *
 * @param {SearchListParamsType} body - 请求参数
 */
export async function searchQuestion (body: SearchListParamsType) {
  return useHttp<any>(Api.searchQuestion, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取金刚区数据
 */
export async function getFixedModuleManage (body: any) {
  return useHttp<any>(Api.getFixedModuleManage, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
