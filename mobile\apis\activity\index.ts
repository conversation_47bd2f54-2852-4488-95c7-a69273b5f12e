import type { ApiResponse } from '../types'
import type {
  UseableCoupon, ReceiveCoupon, CouponCanReceive, BuyGiveGoodsRes, QueryCouponAmount, QueryMyCoupon, QuerySeckillList,
  FetchBuyAtMarkupGoodsListParams,
  FetchBuyAtMarkupGoodsListDTO,
  FetchBuyGiveProductListParams,
  FetchBuyGiveProductListDTO,
  FetchGetGoodsDiscountsParams,
  FetchGetGoodsDiscountDTO,
  PirceCouponListParam,
  PirceCouponListData,
  PirceCouponDetailParam,
  PirceCouponDetailData,
  PirceCouponOrderParam,
  PirceCouponOrderData,
  refundVerifyParam,
  refundVerifyData
} from './types'
enum activity {
  findUsableCouponsProt = '/kukecoupon/wap/coupon/findUsableCouponsProt',
  userGetCouponsProt = '/kukecoupon/wap/coupon/userGetCouponsProt',
  userCouponsGetProt = '/kukecoupon/wap/coupon/userCouponsGetProt',
  queryCouponAmount = '/kukecoupon/wap/coupon/queryCouponAmount',
  userCouponsProt = '/kukecoupon/wap/coupon/userCouponsProt',
  couponGoods = '/kukecoupon/wap/marketingGoods/getCouponDetailAndGoods',
  seckillGoodsList = '/kukecoupon/wap/marketingGoods/seckillGoodsList',
  groupList = '/kukecoupon/wap/marketingGoods/groupList',
  receiveTemp = 'kukecoupon/wap/marketingGoods/receiveTemplate',
  getPromotionCouponDetails = '/kukecoupon/wap/marketingGoods/getPromotionPage',
  createOrderGetCoupon = '/kukecoupon/wap/marketingGoods/createOrderGetCoupon',
  getBuyGiveGoods = '/kukecoregoods/wap/goods/getBuyGiveGoods',
  getGroupTeamStatus = '/kukecoupon/wap/group/getGroupTeamStatus',
  freeGroupTeamStatus = '/kukecoupon/wap/group/freeGroupTeamStatus',
  findDiscountCoupon = '/kukecoupon/wap/marketingGoods/findDiscount',
  fetchBuyAtMarkupGoodsList = '/kukecoupon/wap/marketingGoods/getBuyAtMarkupGoods',
  fetchBuyGiveProductList = '/kukecoupon/wap/marketingGoods/getBuyGiveGoods',
  fetchGetGoodsDiscounts = '/kukecoupon/wap/marketingGoods/getGoodsDiscounts',
  pirceCouponList = '/kukecoupon/wap/marketingGoods/getValuedCouponList',
  pirceCouponDetail = '/kukecoupon/wap/marketingGoods/getValuedCouponDetail',
  addCouponOrder = '/kukeonlineorder/wap/order/addCouponOrderProt',
  refundVerify = '/kukecoupon/api/kcValuedCoupon/refundVerify',
}
// 查询可用优惠券及活动
export async function findUsableCouponsProt (body: UseableCoupon) {
  return useHttp<any>(activity.findUsableCouponsProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 查询可领优惠券
export async function userGetCouponsProt (body: CouponCanReceive) {
  return useHttp<any>(activity.userGetCouponsProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 优惠券领取
export async function userCouponsGetProt (body: { couponId: string }) {
  return useHttp<ReceiveCoupon>(activity.userCouponsGetProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 订单确认页查询优惠金额
export async function queryCouponAmount (body: QueryCouponAmount) {
  return useHttp<any>(activity.queryCouponAmount, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 我的优惠券
export async function userCouponsProt (body: QueryMyCoupon) {
  return useHttp<any>(activity.userCouponsProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 秒杀列表
export async function seckillGoodsList (body: QuerySeckillList) {
  return useHttp<any>(activity.seckillGoodsList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 拼团列表
export async function groupList (body: any) {
  return useHttp<any>(activity.groupList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 可使用优惠券商品列表
export async function couponGoodsList (body: any) {
  return useHttp<any>(activity.couponGoods, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 优惠券领取
export async function receiveTemp (body: any) {
  return useHttp<any>(activity.receiveTemp, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 优惠券详情
export async function getPromotionCouponDetails (body: { id: string }) {
  return useHttp<any>(activity.getPromotionCouponDetails, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取买赠商品的优惠券和商品列表
export async function getBuyGiveGoods (body: { goodsMasterId: string, specificationItemId?: string }) {
  return useHttp<ApiResponse<BuyGiveGoodsRes>>(activity.getBuyGiveGoods, {
    method: 'post',
    body,
  })
}

// 下发权益优惠券
export async function createOrderGetCoupon (body: { orderSn: string, couponIds: string, goodsMasterId: string, specificationItemId?: string, buyGiveId: string, shouldPay: string | number }) {
  return useHttp<any>(activity.createOrderGetCoupon, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 根据用户和团id判断是否参与过此团
export async function getGroupTeamStatus (body: { teamId: string }) {
  return useHttp<ApiResponse<{ teamStatus: number, orderSn: string }>>(activity.getGroupTeamStatus, {
    method: 'post',
    body,
  })
}

// 获取0元拼团后领取成功后的 拼团信息
export async function getFreeGroupTeamStatus (body: { orderSn: string }) {
  return useHttp<any>(activity.freeGroupTeamStatus, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 获取下单优惠券信息
export async function findDiscountCoupon (body: { activityId: string, activityType: number, goodsMasterId: string, goodsSpecificationItemId?: string }) {
  return useHttp<{
    coupon: {
      couponAmount: number,
      couponId: string
    }
  }>(activity.findDiscountCoupon, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取换购商品列表
 * @param body
 * @returns
 */
export async function fetchBuyAtMarkupGoodsList (body: FetchBuyAtMarkupGoodsListParams) {
  return useHttp<{list:FetchBuyAtMarkupGoodsListDTO[]}>(activity.fetchBuyAtMarkupGoodsList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取买赠产品列表
 * @param body
 * @returns
 */
export async function fetchBuyGiveProductList (body: FetchBuyGiveProductListParams) {
  return useHttp<FetchBuyGiveProductListDTO>(activity.fetchBuyGiveProductList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取商品可用优惠券列表
 * @param body
 * @returns
 */
export async function fetchGetGoodsDiscounts (body : FetchGetGoodsDiscountsParams) {
  return useHttp<FetchGetGoodsDiscountDTO>(activity.fetchGetGoodsDiscounts, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 查询有价优惠券活动列表
 * @param body
 * @returns
 */
export async function pirceCouponList (body : PirceCouponListParam) {
  return useHttp<PirceCouponListData>(activity.pirceCouponList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 * 查询有价优惠券活动详情
 * @param body
 * @returns
 */
export async function pirceCouponDetail (body : PirceCouponDetailParam) {
  return useHttp<PirceCouponDetailData>(activity.pirceCouponDetail, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 * 优惠券下单
 * @param body
 * @returns
 */
export async function addCouponOrder (body : PirceCouponOrderParam) {
  return useHttp<PirceCouponOrderData>(activity.addCouponOrder, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 * 有价优惠券订单退款校验接口
 * @param body
 * @returns
 */
export async function refundVerify (body : refundVerifyParam) {
  return useHttp<refundVerifyData>(activity.refundVerify, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}
