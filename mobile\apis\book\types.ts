export interface TabType {

    /**
     * 名称
     */

    title:string,

    /**
     * id
     */

    id:number
}

export interface PagePrams {
    [key:string] : string,
    // /**
    //  * 专业分类id
    //  */
    // cateId?:number

    // /**
    //  * 地区
    //  */
    // regionId?: string,

    // /**
    //  * 科目
    //  */
    // subjectId?: string,

    // /**
    //  * 学段
    //  */
    // academicSectionId?: string,

    // /**
    //  * 考试形式
    //  */
    // examFormatId?: string,

    // /**
    //  * 方向
    //  */
    // directionId?: string
}

export interface PageListPrams {

    /**
     * 商品名称
     */

    goodsTitle?: string,

    /**
     * 专业分类id
     */

    cateId?: string,

    /**
     * 页码
     */

    page: number,

    /**
     * 分页数
     */

    pageSize: number,

    /**
     * 地区
     */
    regionId?: string,

    /**
     * 科目
     */
    subjectId?: string,

    /**
     * 学段
     */
    academicSectionId?: string,

    /**
     * 考试形式
     */
    examFormatId?: string,

    /**
     * 方向
     */
    directionId?: string

}

export interface PageListItem {

    /**
     * 创建时间
     */

    createdAt:string,

    /**
     * 勘误数量
     */

    errataCount:number,

    /**
     * 商品图片
     *
     */

    goodsImg:string,

    /**
     * 商品id
     */

    goodsMasterId:string,

    /**
     * 商品名称
     */

    goodsTitle:string,

    /**
     * 图书勘误id
     */

    id:string,

    /**
     * 是否展示 0否 1是
     */

    isShow:number,

    /**
     * 更新时间
     */

    updatedAt:string

}

export interface PageListRes {

    /**
     * count
     */

    count:number,

    /**
     * list
     */

    list:PageListItem[]

}

export interface CateParams {

    /**
     * 产品id
     */
    usedId?:string | null,

    /**
     * 分类名称
     */

    categoryName?:string,

    /**
     * 状态
     */
    status?:number

}

export interface CateItem {

    /**
     * 分类名称
     */

    categoryName:string,

    /**
     * 标签项id
     */

    id:string,

    /**
     * 分类等级
     */

    level:number,

    /**
     * 使用id
     */

    usedId:number,

}

export interface CateRes {

    /**
     * count
     */

    count:number,

    /**
     * 标签项列表
     */

    list:CateItem[],

}

export interface SecondCateParams {

    /**
     * 专业分类useid
     */
    categoryUsedId?: number,

}

export interface listTagType {

    /**
     * 标签值id
     */

    id:number,

    /**
     * 标签值名称
     */

    title:string

}

export interface SecondCateItem {

    /**
     * 标签项类别
     */
    field: string,

    /**
     * 标签值名称
     */
    name: string,

    /**
     * 标签值列表
     */
    list: listTagType[]

}

export interface SecondCateRes {

    list:SecondCateItem[]

    /**
     * 标签项类别
     */

    count:number
}

export interface DetailPrams {

    /**
     * 图书勘误id
     */

    bookErrataId:string,

    /**
     * 页码
     */

    page: number,

    /**
     * 分页数
     */

    pageSize: number

}

export interface DetailItemType {

    /**
     * 图书勘误id
     */

    bookErrataId:string,

    /**
     * 正确内容
     */

    correctContent:string,

    /**
     * 创建时间
     */

    createdAt:string,

    /**
     * 错误内容
     */

    errorContent:string,

    /**
     * id
     */

    id:string,

    /**
     * 是否展示 0否 1是
     */

    isShow:number,

    /**
     * 页码
     */

    pageNum:string,

    /**
     * 更新时间
     */

    updatedAt:string,

    updateAdminId:string,

    createAdminId:string,

    deletedAt:string,
}

export interface DetailListRes {

    /**
     * count
     */

    count:number,

    /**
     * list
     *
     * @type {DetailItemType[]}
     */

    list:DetailItemType[]
}

export interface DetailRes {
    /**
     * 创建时间
     */
    createdAt:string,

    /**
     * 勘误数量
     */
    errataCount:number,

    /**
     * 商品图片
     */
    goodsImg:string,

    /**
     * 商品id
     */
    goodsMasterId:string,

    /**
     * 商品名称
     */
    goodsTitle:string,
    /**
     * 图书勘误id
     */
    id:string,

    /**
     * 是否展示 0否 1是
     */
    isShow:number,

    /**
     * 更新时间
     */
    updatedAt:string

}
