// ssr-env-module.ts
import { defineNuxtModule } from '@nuxt/kit'

/**
 * @description 构建环境更改 nuxt 配置信息
 */
export default defineNuxtModule({
  meta: {
    name: 'ssr-env-module',
  },
  defaults: {
    // 默认配置
    env: ['PROD'],
  },
  hooks: {
    // 钩子
    ready: (nuxt) => {
      if (!process.env?.NUXT_BUILD_ENV) { return }
      console.log('[ssr-env-module] 当前 SSR 配置的值:', nuxt.options.ssr)
    }
  },
  setup (options, nuxt) {
    if (!process.env?.NUXT_BUILD_ENV) { return }
    console.log('[ssr-env-module] 当前环境变量 NUXT_BUILD_ENV:', process.env.NUXT_BUILD_ENV)
    console.log('[ssr-env-module] 原始 SSR 配置:', nuxt.options.ssr)

    // 根据配置的环境在运行/构建时 是否强制开启服务端渲染
    if (options.env.includes(process.env.NUXT_BUILD_ENV)) {
      console.log('[ssr-env-module] 满足条件强制开启 SSR 服务')
      nuxt.options.ssr = true
    }
  }
})
