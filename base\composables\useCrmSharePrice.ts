/**
 * crm分享改价
 * scrm 和crm分享改价逻辑一致，所以写到一起
 * @useCrmSharePrice
 */
const useCrmSharePrice = () => {
  const route = useRoute()
  const kkInternalSystem = route.query[INNERNAL_SYSTEM]
  const kkPoolId = route.query[POOL_ID]
  const cookieSaleAdminId = route.query[SALE_ADMIN_ID]
  const kkSharePrice = route.query[SHARE_PRICE]
  const kkShareId = route.query[SHARE_LINK_ID]
  const kkTargetCode = route.query[TARGET_CODE]
  const reqUrlCode = '/kukecrms/appKcGetCustomer/codeShareId'
  const reqUrl = '/kukecrms/kcSaleAdminGoodsPrice/getSharePrice'
  const reqUrlScrm = '/kukescrm/index/goods/getShareGoodsInfo'
  // const reqUrl = 'http://yapi.kukewang.com:9005/mock/558/kukecrms/kcSaleAdminGoodsPrice/getSharePriceProt'

  // 判断是否替换改价
  const sharePriceFlag = () => {
    const isSharePriceOne = kkSharePrice && Number(kkSharePrice) === 1
    // crm的系统标识是26，scrm的系统标识是37
    const isInternalSystemValid = kkInternalSystem && (Number(kkInternalSystem) === 26 || Number(kkInternalSystem) === 37)
    return isSharePriceOne && isInternalSystemValid
  }

  // 获取分享改价金额
  const getSharePrice = async (goodsMasterId: string, specificationItemId?: string,) => {
    try {
      if (Number(kkInternalSystem) === 26 && Number(kkSharePrice) === 1 && (kkShareId || kkTargetCode)) {
        // 如果是获客码链接，先请求接口获取shareid
        let codeShareId = ''
        if (kkTargetCode) {
          const { data } = await useHttp(reqUrlCode, { method: 'post', body: { goodsMasterId, poolId: kkPoolId, saleAdminId: cookieSaleAdminId, specificationItemId } })
          // 获客码链接的商品，没有shareID就按原价展示；有shareID就放在路由参数里面传到提交订单以及后续的收银台的页面，与分享逻辑一致
          if (!data.value.data.shareId) {
            return { sharePrice: undefined, validityStatus: -1 }
          }
          codeShareId = data.value.data.shareId
        }
        // 获取分享价格的接口
        const { data } = await useHttp(reqUrl, { method: 'post', body: { goodsMasterId, poolId: kkPoolId, saleAdminId: cookieSaleAdminId, specificationItemId, shareId: kkShareId || codeShareId } })
        const sharePrice = data?.value?.data?.sharePrice
        const validityStatus = data?.value?.data?.validityStatus
        return { sharePrice, validityStatus, codeShareId }
      } else if (Number(kkInternalSystem) === 37 && Number(kkSharePrice) === 1 && (kkShareId)) {
        // 如果是SCRM链接 获取SCRM分享价格的接口
        const { data } = await useHttp(reqUrlScrm, { method: 'post', body: { goodsMasterId, saleAdminId: cookieSaleAdminId, specificationItemId, shareId: kkShareId } })
        const sharePrice = data?.value?.data?.sharePrice
        const validityStatus = data?.value?.data?.validityStatus
        return { sharePrice, validityStatus }
      } else {
        // 没有shareID并且也不是获客码的商品，就展示链接已失效
        return { sharePrice: undefined, validityStatus: 0 }
      }
    } catch {
      // validityStatus=-1 接口报错
      return { sharePrice: undefined, validityStatus: -1 }
    }
  }

  return {
    sharePriceFlag,
    getSharePrice
  }
}

export default useCrmSharePrice
