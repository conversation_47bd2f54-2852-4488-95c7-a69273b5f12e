import type { GiftPacks } from './types'

enum giftpacksApi {
    claimpage = '/kukecoupon/wap/kcGiftPacks/claimpage',
    shareDetails ='/kukecoupon/wap/kcGiftPacks/couponShareDetails',
    oneClickCollection = '/kukecoupon/wap/kcGiftPacks/oneClickCollection',
    giftPacksStatus = '/kukecoupon/wap/kcGiftPacks/getUserGiftPacksStatus'
}
// 获取领取详情
export async function getGiftPacksDetail (body: any) {
  return useHttp<GiftPacks>(giftpacksApi.claimpage, {
    method: 'post',
    body,
    transform: res => res?.data,
    default () {
      return {}
    }
  })
}

// 大礼包分享
export async function getShareData (body: any) {
  return useHttp<void>(giftpacksApi.shareDetails, {
    method: 'post',
    body,
    transform: res => res.data,
    default () {
      return {}
    }
  })
}

// 大礼包领取
export async function oneClickCollection (body: {id:string, pickupMethod:number}) {
  return useHttp<void>(giftpacksApi.oneClickCollection, {
    method: 'post',
    body,
    transform: res => res?.data,
  })
}

// 获取大礼包状态
export async function getUserGiftPacksStatus (body: {id:string}) {
  return useHttp<{status:number}>(giftpacksApi.giftPacksStatus, {
    method: 'post',
    body,
    transform: res => res?.data,
  })
}
