<template>
  <Loading v-if="loading" :content="content" />
</template>

<script setup lang="ts">
import Loading from './Loading/index.vue'
defineProps({
  device: {
    type: String,
    default: 'MOBILE' // PC
  },
  theme: {
    type: String,
    default: '' // dark
  },
  content: {
    type: String,
    default: '加载中'
  },
})
const nuxtApp = useNuxtApp()
const route = useRoute()
const { disableLoading } = route.meta
const loading = ref(false)
nuxtApp.hook('page:start', () => {
  if (!disableLoading) {
    loading.value = true
  }
})
nuxtApp.hook('page:finish', async () => {
  await nextTick()
  // setTimeout(() => {
  loading.value = false
  // }, 100)
})
nuxtApp.hook('vue:error', () => {
  loading.value = false
})
// nuxtApp.hook('app:mounted', () => {
//   nextTick(() => {
//     document.querySelector('.kkc-spa-loading').style.display = 'none'
//   })
// })
</script>
<style lang="scss"></style>
