import { hash } from 'ohash'
import type {
  IdVO,
  CateIdVO,
  HomeNewsDTO,
  AreaInfoDTO,
  AreaLabelResVo,
  RegionLabelResVO,
  RegionLabelDTO,
  ClassLableInfoResVO,
  CategoryResVO,
  NewsListResVo,
  NewsInfoDTO,
  LabelListDTO,
  QuestionConfirmDTO,
  NewsDetailInfoResVO,
  FetchNewsDetailVisitReportParams
} from './types'

export enum NewApi {
  tabNewsList = '/kukemarketing/wap/pageMaster/tab/list',
  newsList = '/kukecorenews/wap/news/getNewsListWX',
  newsDetail = '/kukecorenews/wap/news/getNewsDetailsWX',
  newsLabel = '/kukecorenews/wap/news/getNewsLabelListWX',
  moreList = '/kukemarketing/wap/pageMaster/news/more/list',
  homeId = '/kukemarketing/wap/pageMaster/getCustomPageDetail',
  categoryList = '/kukebasesystem/wap/ksProduct/getClientCategory',
  categoryNewsList = '/kukecorenews/wap/news/getCatIdNewsClassifyList',
  addQuestion = '/kukecorenews/wap/news/addNewsQuestionProt',
  areaList = '/kukebasesystem/wap/ksLabel/regionLabelList',
  areaChildList = '/kukebasesystem/wap/baseArea/areaChild',
  viewsNums = '/kukecorenews/wap/news/statisticalViews', // 浏览量埋点
  newsNums = '/kukecorenews/wap/news/getViewsById', // 浏览量
  recommed = '/kukecoregoods/wap/goods/courseRecommendByCategory', // 专业分类-好课推荐
  relatedRecommend = '/kukecorenews/wap/newsWXCustomization/description', // 相关推荐列表
  getHotSearchNewsList = '/kukecorenews/wap/newsWXCustomization/getHotSearchNewsList', // 热搜排行列表
  topNews = '/kukecorenews/wap/newsWXCustomization/topNews', // 置顶资讯列表
  topNewsById = '/kukecorenews/wap/newsWXCustomization/topNewsById', // 置顶资讯列表
  getNewsCategoryAll = '/kukecorenews/wap/news/getLabelList', //
  getNewsMap = '/kukecorenews/wap/newsWXCustomization/getNewsMap', //
  indexClassifyNewsNews = '/kukecorenews/news/indexClassifyNewsNews', //
}

// 组件内tab切换的资讯列表查询
export async function getNewsListToBussiness (body: any) {
  return useHttp<any>(NewApi.tabNewsList, {
    method: 'post',
    body
  })
}

// 获取资讯首页id
export async function getNewsHomeId (body: HomeNewsDTO) {
  return useHttp<any>(NewApi.homeId, {
    key: NewApi.homeId + body.productSide + body.code,
    method: 'post',
    body
  })
}

// 获取地区接口
export async function getAreaChildList (body: AreaInfoDTO) {
  return useHttp<{ data: AreaLabelResVo }>(NewApi.areaChildList, {
    key: NewApi.areaChildList + body?.parentId,
    method: 'post',
    body
  })
}
// 获取地区接口 v2
export async function getAreaChildListV2 (body: AreaInfoDTO) {
  return useHttp<{ data: AreaLabelResVo }>(NewApi.areaChildList, {
    key: NewApi.areaChildList + '_' + body?.parentId,
    method: 'post',
    body,
    transform (res) {
      return res?.data?.list || []
    }
  })
}

// 获取省份标签列表接口
export async function getAreaLabelList (body: RegionLabelDTO) {
  return useHttp<{ data: RegionLabelResVO }>(NewApi.areaList, {
    method: 'post',
    body
  })
}

// 获取专业分类
export async function getCategoryList () {
  return useHttp<{ data: CategoryResVO }>(NewApi.categoryList, {
    method: 'post'
  })
}

// 获取资讯分类列表
export async function getCateNewsLabelList (body: IdVO) {
  return useHttp<{ data: ClassLableInfoResVO }>(NewApi.categoryNewsList, {
    method: 'post',
    body
  })
}

// 获取资讯数据列表
export async function getNewsList (body: NewsInfoDTO) {
  return useHttp<{ data: NewsListResVo }>(NewApi.newsList, {
    method: 'post',
    body
  })
}

// 根据标签获取资讯列表
export async function getNewsLabelList (body: LabelListDTO) {
  return useHttp<{ data: NewsListResVo }>(NewApi.newsLabel, {
    method: 'post',
    body
  })
}

// 资讯列表页
export async function getNewsMoreList (body: any) {
  return useHttp<any>(NewApi.moreList, {
    method: 'post',
    body
  })
}

// 资讯详情
export async function getNewsDetailInfo (body: IdVO) {
  return useHttp<{ data: NewsDetailInfoResVO }>(NewApi.newsDetail, {
    method: 'post',
    body
  })
}
/**
 * 专用接口
 * 小程序审核状态下的资讯列表
 * @param body
 * @returns
*/
export async function getNewsListTemp (body: IdVO) {
  return useHttp<{ data: NewsDetailInfoResVO }>('/kukecorenews/wap/news/getNewsCommonList', {
    method: 'post',
    body
  })
}
/**
 * 专用接口
 * 小程序审核状态下的资讯详情
 * @param body
 * @returns
*/
export async function getNewsDetailInfoTemp (body: IdVO) {
  return useHttp<{ data: NewsDetailInfoResVO }>('/kukecorenews/wap/news/getNewsCommonDetails', {
    method: 'post',
    body
  })
}

// 提交问答
export async function addQuestion (body: QuestionConfirmDTO) {
  return useHttp<any>(NewApi.addQuestion, {
    method: 'post',
    body
  })
}

// 资讯页访问埋点
export async function setViewNums (body: FetchNewsDetailVisitReportParams) {
  return useHttp<any>(NewApi.viewsNums, {
    method: 'post',
    body
  })
}

// 资讯页浏览量
export async function getViewNums (body: IdVO) {
  return useHttp<any>(NewApi.newsNums, {
    method: 'post',
    body
  })
}

// 根据专业分类获取好课推荐
export async function getRecommed (body: CateIdVO) {
  return useHttp<any>(NewApi.recommed, {
    method: 'post',
    body
  })
}

// 获取相关推荐列表
export async function getRelatedRecommend (body: NewsInfoDTO) {
  return useHttp<{ data: NewsListResVo }>(NewApi.relatedRecommend, {
    method: 'post',
    body
  })
}

// 获取热点资讯列表
export async function getHotSearchNewsList (body: NewsInfoDTO) {
  return useHttp<{ data: NewsListResVo }>(NewApi.getHotSearchNewsList, {
    method: 'post',
    body
  })
}

// 获取置顶资讯列表
export async function getTopNews (body: NewsInfoDTO) {
  return useHttp<{ data: NewsListResVo }>(NewApi.topNews, {
    method: 'post',
    body
  })
}
// 获取置顶资讯列表
export async function topNewsById (body: NewsInfoDTO) {
  return useHttp<{ data: NewsListResVo }>(NewApi.topNewsById, {
    method: 'post',
    body
  })
}
/**
 * 专业分类列表
 * 资讯分类列表
 * 地区列表
 * @param body
 * @returns
*/
export async function getNewsCategoryAll (body: any) {
  const _api = NewApi.getNewsCategoryAll
  const key = hash({
    ...body,
    _api
  })
  return useHttp<{ data: any }>(NewApi.getNewsCategoryAll, {
    key,
    method: 'post',
    body,
    transform: res => res.data,
  })
}
/**
 * 网站地图
 * 所有分类接口
 *
 * @param body
 * @returns
*/
export async function getNewsMap (body: any = {}) {
  return useHttp<{ data: any }>(NewApi.getNewsMap, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
/**
 * web端首页定制 公考资讯
 *
 * @param body
 * @returns
*/
export async function indexClassifyNewsNews (body: any = {}) {
  return useHttp<{ data: any }>(NewApi.indexClassifyNewsNews, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
/**
 * web端定制 首页热门资讯/专升本资讯
 *
 * @param body
 * @returns
*/
export async function indexHotNews (body: any = {}) {
  return useHttp<{ data: any }>('/kukecorenews/news/indexHotNews', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
