<template>
  <div>
    <form class="form-content" onsubmit="return false">
      <slot name="default" />
      <div class="form-submit">
        <slot name="submit" />
      </div>
    </form>
  </div>
</template>

<script lang='ts' setup>
import { emitter, type Fun } from './hooks'

const iptArr: Fun[] = []
const callBack = (params: Fun) => {
  iptArr.push(params)
}
// TODO 内存泄漏可疑点: emitter事件应该放到onMounted，并及时去除事件绑定
emitter.subscribe('markeingListenSubmit', callBack)
/* 组件销毁同时停止监听 */
onUnmounted(() => {
  emitter.unsubscribe('markeingListenSubmit')
})
/* 表单提交 */
const validate = (callback: Function) => {
  console.log('iptArr', iptArr)
  const arr: any[] = []
  iptArr.forEach((item) => {
    arr.push(String(item()))
  })
  const res = !arr.includes('false')

  callback(res)
}

defineExpose({
  validate
})
</script>
<style scoped></style>
