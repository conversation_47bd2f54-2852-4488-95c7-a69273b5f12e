import type { ChatHistoryRes, ChatLogListParams, FirstSendChat, SendChat } from './chat.type'
import type { AllTagParams, AllTagReturn, CategoryParams, GoodsTagParams, GoodsTagReturn, ListParams, ListReturn, PhotoParams, PhotoReturn, QKParams, QKReturn, } from './types'

enum AnswerApi {
    // 答疑列表
    userList = '/kukeanswerhive/wapUser/kaRequest/getWapUserRequestListProt',
    // 拍照搜题
    photoSearchQuestionProt = '/kukeanswerhive/interractive/mobile/photoAiSolveQuestionProt',
    // 首次发起答疑
    firstSendAnswer = '/kukeanswerhive/interractive/mobile/firstSendMessageProt',
    // 发送消息
    sendAnswer = '/kukeanswerhive/interractive/mobile/sendMessageProt',
    // 获取对话记录
    getChatLog = '/kukeanswerhive/interractive/mobile/getHistoryListProt',
    // 获取分类列表
    fetchCategoryList = '/kukeanswerhive/interractive/mobile/getSelectOptionsProt',
    // 获取商品关联的标签
    fetchGoodsTags = '/kukecoregoods/goods/getGoodsTags',
    // 获取默认组织标签值
    fetchAllGoodsTag = 'kukebasesystem/ksCategory/queryCategoryProt',
    // 题库答题 切换试题
    fetchQKById = '/kukecorequestion/outer/answer/getKqQuestionListProt',
    // 答疑解决
    questionDone = '/kukeanswerhive/interractive/mobile/questionDoneProt',
    // 获取用户信息
    getAnswerUserInfo = '/kukeanswerhive/interractive/pc/getUserInfoProt',
    // 题目框选信息
    getQusetionCoordinate = '/kukeanswerhive/interractive/mobile/getQusetionCoordinateProt',
    // 语音转文字
    getAudioText = '/kukeopen/audio/audioToTextProt',
    // 设置消息已读
    setReadMsg = '/kukeanswerhive/interractive/mobile/readMsgProt',
    // 用户答疑权益校验
    getUserEquityVerifyPort = '/kukeanswerhive/wap/kaEquitySummary/userEquityVerifyProt',
    // 用户拍搜权益校验
    getPhotoEquityVerifyPort = '/kukeanswerhive/wap/kaEquitySummary/queryPhotoEquityByUserIdProt',
    // 答疑权益列表
    getEquityList = '/kukeanswerhive/appletEquity/KaEquitySummary/queryKaEquitySummaryListProt',
    // 答疑权益详情
    getEquityDetail = '/kukeanswerhive/appletEquity/KaEquitySummary/queryKaEquitySummaryInfoProt',
    // 获取权益专业分类
    getCateList = '/kukeanswerhive/wap/kaEquitySummary/queryCateListByUserIdProt',
    // 获取权益标签
    getEquityLabelList = '/kukeanswerhive/wap/kaEquitySummary/userEquityLabelProt',
    // 查看人工答疑服务设置
    getManualService = '/kukeanswerhive/kaRuleConfig/queryManualServiceProt',
    // 查看拍搜服务设置
    getSearchService = '/kukeanswerhive/kaRuleConfig/querySearchServiceProt',
    // 拍搜记录列表
    getPhotoSearchRecordListProt = '/kukeanswerhive/interractive/mobile/getPhotoSearchRecordListProt',
    // 拍搜记录详情
    getPhotoSearchRecordDetailProt = '/kukeanswerhive/interractive/mobile/getPhotoSearchRecordDetailProt'

}

/**
 *
 * @param body
 * 获取答疑列表
 *
 */

export async function fetchAnswerList (body : ListParams) {
  return useHttp<ListReturn>(AnswerApi.userList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *
 * @param body
 * 拍照搜题
 *
 */

export async function fetchAnswerPhoto (body : PhotoParams) {
  return useHttp<PhotoReturn>(AnswerApi.photoSearchQuestionProt, {
    method: 'post',
    body,
    timeout: 60 * 1000,
    transform: (res: any) => res.data,
  })
}
/**
 * 首次发起答疑（创建新答疑）
 */
export async function firstSendAnswer (body:FirstSendChat) {
  return useHttp<IResponse<any>>(AnswerApi.firstSendAnswer, {
    method: 'post',
    body,
    transform: (res: any) => res,
  })
}
/**
 * 发送消息
 */
export async function sendAnswer (body:SendChat) {
  return useHttp<IResponse<any>>(AnswerApi.sendAnswer, {
    method: 'post',
    body,
    transform: (res: any) => res,
  })
}
/**
 * 获取对话记录
 */
export async function getChatLog (body:ChatLogListParams) {
  return useHttp<IResponse<ChatHistoryRes>>(AnswerApi.getChatLog, {
    method: 'post',
    body,
    transform: (res: any) => res,
  })
}

/**
 * 获取分类列表
 */
export async function fetchCategoryList (body: CategoryParams) {
  return useHttp<string>(AnswerApi.fetchCategoryList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 获取商品关联的标签
 */

export async function fetchGoodsTags (body: GoodsTagParams) {
  return useHttp<GoodsTagReturn>(AnswerApi.fetchGoodsTags, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 获取默认组织标签值
 */
export async function fetchAllGoodsTags (body: AllTagParams) {
  return useHttp<AllTagReturn>(AnswerApi.fetchAllGoodsTag, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *
 * @param
 * 切换题库试题
 *
 */

export async function fetchQKById (body: QKParams) {
  return useHttp<QKReturn>(AnswerApi.fetchQKById, {
    method: 'post',
    body,
    key: JSON.stringify(body),
    transform: (res: any) => res.data,
  })
}

/**
 * 答疑解决
 */

export async function questionDone (body: {id:string}) {
  return useHttp<any>(AnswerApi.questionDone, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 获取答疑用户信息
 */

export async function getAnswerUserInfo (body: {id:string}) {
  return useHttp<any>(AnswerApi.getAnswerUserInfo, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 获取答疑用户信息
 */

export async function getQusetionCoordinate (body:any) {
  return useHttp<any>(AnswerApi.getQusetionCoordinate, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *
 * 语音转文字
 *
 */

export async function getAudioText (body:any) {
  return useHttp<any>(AnswerApi.getAudioText, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 设置消息已读
 */

export async function setReadMsg (body:any) {
  return useHttp<any>(AnswerApi.setReadMsg, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 用户答疑权益校验
 */

export async function getUserEquityVerifyPort (body:any) {
  return useHttp<any>(AnswerApi.getUserEquityVerifyPort, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 * 用户拍搜权益校验
 */

export async function getPhotoEquityVerifyPort (body:any) {
  return useHttp<any>(AnswerApi.getPhotoEquityVerifyPort, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 *
 * 答疑权益列表
 *
 */
export async function getEquityList (body:any) {
  return useHttp<any>(AnswerApi.getEquityList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 *
 *
 * 答疑权益详情
 *
 */

export async function getEquityDetail (body:any) {
  return useHttp<any>(AnswerApi.getEquityDetail, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 *
 * 权益专业分类列表
 *
 */

export async function getEquityCateList (body:any) {
  return useHttp<any>(AnswerApi.getCateList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/***
 *
 * 获取权益标签
 *
 */

export async function getEquityLabelList (body:any) {
  return useHttp<any>(AnswerApi.getEquityLabelList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 *
 * 查看人工答疑服务设置
 *
 */
export async function getManualService (body:any) {
  return useHttp<any>(AnswerApi.getManualService, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

/**
 *
 * 查看拍搜服务设置
 *
 */
export async function getSearchService (body:any) {
  return useHttp<any>(AnswerApi.getSearchService, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    isShowLoading: true
  })
}

// 拍搜记录列表
export async function getPhotoSearchRecordListProt (body: any) {
  return useHttp<any>(AnswerApi.getPhotoSearchRecordListProt, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

// 拍搜记录详情
export async function getPhotoSearchRecordDetailProt (body: any) {
  return useHttp<any>(AnswerApi.getPhotoSearchRecordDetailProt, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
