@include ns(form-input) {
  display: inline-block;
  position: relative;

  .suffix-icon,
  .prefix-icon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: flex-end;

    i {
      font-size: 20px;
      cursor: pointer;
      // transition: all 0.3s;
      width: 25px;
      height: 25px;
      text-align: center;
      line-height: 25px;
    }
  }

  .prefix-icon {
    left: 3px;
    right: auto;
  }

  .icon-close {
    display: none;
  }

  &:hover {
    .icon-close {
      display: block;
      cursor: pointer;
    }
  }

  .has-prefix {
    padding-left: 30px;
  }

  &.input-prepend,
  &.input-append {
    display: flex;
    justify-content: space-between;
    align-items: stretch;

    .prepend,
    .append {
      border: 1px solid $borderColor;
      display: flex;
      align-items: center;
      padding: 0 5px;
    }

    .prepend {
      border-radius: 3px 0 0 3px;
      border-right: none;
      margin-right: -1px;
    }

    .append {
      border-radius: 0 3px 3px 0;
      border-left: none;
      margin-left: -1px;
    }
  }
}

@include ns(input-control) {
  height: 36px;
  line-height: 36px;
  /* border: 1px solid $borderColor; */
  border-radius: $borderRadius;
  background: none;
  outline: none;
  padding: 0 10px;
  font-size: 14px;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
  transition: all 0.3s;

  // &:hover {
  //   opacity: 0.7;
  // }

  &:focus,
  &.focus {
    border-color: $primaryColor;
  }

  &::placeholder,
  &.placeholder {
    color: $placeholder;
  }

  /*禁用状态*/
  &.disabled {
    cursor: not-allowed;
    opacity: $disabledOpacity;
  }

  &.large {
    height: 40px;
    line-height: 40px;
  }

  &.small {
    height: 32px;
    line-height: 32px;
  }

  &.mini {
    height: 28px;
    line-height: 28px;
  }
}