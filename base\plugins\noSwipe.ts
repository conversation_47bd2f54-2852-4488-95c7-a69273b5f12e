interface NoSwipeHandlers {
  touchstart: (event: TouchEvent) => void
  touchmove: (event: TouchEvent) => void
}

declare global {
  interface HTMLElement {
    _noSwipeHandlers?: NoSwipeHandlers
  }
}

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('no-swipe', {
    mounted (el: HTMLElement) {
      let startX: number, startY: number

      // 定义事件处理函数
      const handleTouchStart = (event: TouchEvent) => {
        startX = event.touches[0].clientX
        startY = event.touches[0].clientY
      }

      const handleTouchMove = (event: TouchEvent) => {
        const currentX = event.touches[0].clientX
        const currentY = event.touches[0].clientY

        const deltaX = currentX - startX
        const deltaY = currentY - startY

        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          // 阻止左右滑动
          event.preventDefault()
          event.stopPropagation()
        }

        startX = currentX
        startY = currentY
      }

      // 绑定事件监听器
      el.addEventListener('touchstart', handleTouchStart, { passive: false })
      el.addEventListener('touchmove', handleTouchMove, { passive: false })

      // 将事件处理函数存储在元素上，以便在 unmounted 时移除
      el._noSwipeHandlers = {
        touchstart: handleTouchStart,
        touchmove: handleTouchMove,
      }
    },

    unmounted (el: HTMLElement) {
      // 移除事件监听器
      if (el._noSwipeHandlers) {
        const { touchstart, touchmove } = el._noSwipeHandlers
        el.removeEventListener('touchstart', touchstart)
        el.removeEventListener('touchmove', touchmove)
        delete el._noSwipeHandlers // 清理引用
      }
    },
  })
})
