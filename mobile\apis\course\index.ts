import { hash } from 'ohash'
import type {
  GoodsTagDTO,
  FetchGoodsTagParamsType,
  CourseDetailModel,
  AddCollectParams,
  SpecsInfoParams,
  GoodsSpecificationItem,
  CheckGoodsParams,
  CheckGoodsModel,
  FetchGoodsFieldListParams,
  FetchGoodsFieldListResponseData,
  TeacherParams,
  TeacherResponseData,
  btnStatusParam,
  btnStatusResponseData,
  miniResponseData,
  SKUPrice,
  FetchGoodsDetailsPageConfigParams,
  FetchGoodsDetailsPageConfigDTO,
  TeachingMaterialsDTO,
  CollectionsListQuery,
  CollectionsDetailQuery,
  CollectionResponse,
  CollectionsDetailResponse
} from './types'
import type { ActivityData } from './activetyTypes'
enum Course {
    // 商品详情接口拆分接口
  baseInfo = '/kukecoregoods/wap/goods/getBaseInfo',
  packageList = '/kukecoregoods/wap/goods/getPackageList',
  getCourseNodeList = '/kukecoregoods/wap/goods/getCourseNodeList',
  getTestPapers = '/kukecoregoods/wap/goods/getTestpapers',
  getTeacherList = '/kukecoregoods/wap/goods/getTeachers',
  getModuleManageInfo = '/kukecorequestion/wap/goods/getModuleManageInfo',
  getInfoList = '/kukecorequestion/wap/goods/getInfoList',

  detail = '/kukecoregoods/wap/goods/getDetail',
  getCrmGoodsDetail = '/kukecoregoods/wap/goods/getCrmGoodsDetail',
  getCrmTryListenLeftValiditySecond = '/kukecoregoods/wap/goods/getCrmTryListenLeftValiditySecond',
  onlineList = '/kukecoregoods/wap/goods/getCourseList',
  omoList = '/kukecoregoods/wap/goods/getOmoList',
  experienceList = '/kukecoregoods/wap/goods/getFreeList',
  bookList = '/kukecoregoods/wap/goods/getBookList',
  changeCollect = '/kukecoreuser/wap/collect/changeStatusProt',
  getSpecsInfo = '/kukecoregoods/wap/goods/getSpecificationItemDetail',
  checkGoodsStatus = '/kukecoregoods/wap/goods/getGoodsStatus',
  groupTeamList = '/kukecoupon/wap/marketingGoods/groupTeamList',
  getTestpaperPage = '/kukecoregoods/wap/goods/getTestpaperList',
  marketingInfo = '/kukecoupon/wap/marketingGoods/getActivity',
  bindingDistributionRelation = '/kukecoupon/wap/marketingGoods/bindingDistributionRelation',
  distrpPosterRecord = '/kukecoupon/wap/distribution/distributionPic',
  offLineList = '/kukecoregoods/wap/goods/getOfflineCourseList',
  collectionsList = '/kukecoregoods/wap/goods/getCollectionsList',
  audioList = '/kukecoregoods/wap/goods/getAudioGoodsList',
  collectionsDetail = '/kukecoregoods/wap/goods/getCollectionsDetail',
  getOfficialRecommend = '/kukecoregoods/wap/index/officialRecommend',
  fetchGoodsFieldList = '/kukecoregoods/wap/field/getFields',
  getDistributeSuperviseTeacherInfoProt = '/kukestudentservice/wap/KssUserBuyUnitGoods/getDistributeSuperviseTeacherInfoProt',
  getDistributeSuperviseTeacherListProt = '/kukestudentservice/wap/KssUserBuyUnitGoods/getDistributeSuperviseTeacherListProt',
  adGoods = '/kukecoregoods/wap/goods/adGoods',
  adGoodsByCart = '/kukecoregoods/wap/goods/batchGetAdGoods',
  getBtnStatus = '/kukecoregoods/wap/goods/getBtnStatus',
  getMiniProgramStatus = '/kukemarketing/wap/pageMaster/getMiniProgramStatus',
  getAllMultiSpecs = '/kukecoregoods/wap/goods/calculatePriceRange',
  isServiceGoodsV2 = '/kukestudentservice/kssServiceGoods/isServiceGoodsV2',
  fetchGoodsDetailsPageConfig = '/kukemarketing/wap/pageMaster/getGoodsDetailsPageConfig',
  fetchTeachingMaterialsList = '/kukecoregoods/wap/goods/getHandoutList',
  getTagsByGoodsIdProt = '/kukecorequestion/wap/goods/getTagsByGoodsIdProt',
  getTagsByGoodsRightProt = '/kukecorequestion/wap/goods/getTagsByGoodsRightProt',
  hotGoodsList = '/kukegoods/wap/hotGoodsList',
  getEbookList = '/kukecoregoods/wap/goods/getEbookList',
}

/**
 * @name: 获取商品基本信息
 * @param {object} body
 * @description: 不包括套餐内容和讲师
 * @return {*}
 */
export async function getBaseInfo (body: { id: string, goodsSpecificationItemId?: string }) {
  return useHttp<CourseDetailModel>(Course.baseInfo, {
    key: Course.detail + body?.id,
    method: 'post',
    body,
  })
}
/**
 * @name: 获取套餐内容
 * @param {object} body
 * @description: 获取套餐内容不包括
 * @return {*}
 */
export async function getPackageList (body: { id: string, goodsSpecificationItemId?: string }) {
  return useHttp<CourseDetailModel>(Course.packageList, {
    key: Course.packageList + body?.id,
    method: 'post',
    body,
  })
}
/**
 * @name: 获取课时列表
 * @param {object} body
 * @description: 获取目录和套餐下的章节课时数据
 * @return {*}
 */
export async function getCourseNodeList (body: { id: string, goodsSpecificationItemId?: string }) {
  return useHttp<CourseDetailModel>(Course.getCourseNodeList, {
    key: Course.getCourseNodeList + body?.id,
    method: 'post',
    body,
  })
}
/**
 * @name: 获取试卷列表
 * @param {object} body
 * @description: 获取试卷套餐列表
 * @return {*}
 */
export async function getTestPapersList (body: { id: string, goodsSpecificationItemId?: string }) {
  return useHttp<CourseDetailModel>(Course.getTestPapers, {
    key: Course.getTestPapers + body?.id,
    method: 'post',
    body,
  })
}
/**
 * @name: 题库模块列表
 * @param {object} body - 请求参数
 * @param {string} body.moduleManageId - 模块管理ID
 * @param {number} body.studyLevel1 - 一级分类id
 * @param {string} [body.tag] - tag参数的key值为examFormat、directionType、region、academicSection、subjectType；存在则有该项，不存在则没有该项（可选）
 * @description: 获取模块的试卷/教材/刷题列表
 * @return {*}
 */
export async function getModuleManageInfoList (body: { moduleManageId: string, studyLevel1: number, tag?: string }) {
  return useHttp<CourseDetailModel>(Course.getModuleManageInfo, {
    key: Course.getModuleManageInfo + body?.moduleManageId,
    method: 'post',
    body,
  })
}
/**
 * @name: 获取题库单品列表
 * @param {object} body - 请求参数
 * @param {string} body.moduleManageId - 模块管理ID
 * @param {string} body.id - 教材/刷题版本id
 * @param {number} body.type - 类型 1章节 2刷题版本
 * @description: 章节/刷题版本目录列表
 * @return {*}
 */
export async function getInfoList (body: { moduleManageId: string, id: string, type: number }) {
  return useHttp<CourseDetailModel>(Course.getInfoList, {
    key: Course.getInfoList + body?.moduleManageId,
    method: 'post',
    body,
  })
}
/**
 * @name: 获取讲师列表
 * @param {object} body
 * @description: 获取课程下所有讲师
 * @return {*}
 */
export async function getTeacherList (body: { id: string, goodsSpecificationItemId?: string }) {
  return useHttp<CourseDetailModel>(Course.getTeacherList, {
    key: Course.getTeacherList + body?.id,
    method: 'post',
    body,
  })
}

/**
 * 请求讲义列表
 * @param body
 * @returns
 */
export async function fetchTeachingMaterialsList (body: { id: string, goodsSpecificationItemId?: string }) {
  return useHttp<{ count: number, list: TeachingMaterialsDTO[] }>(Course.fetchTeachingMaterialsList, {
    key: Course.fetchTeachingMaterialsList + body?.id,
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 详情
export async function detail (body: { id: string }) {
  return useHttp<CourseDetailModel>(Course.detail, {
    key: Course.detail + body?.id,
    method: 'post',
    body,
    // isShowLoading: true
  })
}

// 商品详情(crm使用)
export async function getCrmGoodsDetail (body: { id: string }) {
  return useHttp<CourseDetailModel>(Course.getCrmGoodsDetail, {
    method: 'post',
    body
  })
}

// 商品试听信息(crm使用)
export async function getCrmTryListenLeftValiditySecond (body: { id: string, poolId: string }) {
  return useHttp<any>(Course.getCrmTryListenLeftValiditySecond, {
    method: 'post',
    body
  })
}

// 网课列表
export async function onlineList (body: any) {
  return useHttp<any>(Course.onlineList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 试卷包列表
export async function getTestpaperPage (body: any) {
  return useHttp<any>(Course.getTestpaperPage, {
    body,
    transform: res => res.data,
  })
}
// omo列表
export async function omoList (body: any) {
  return useHttp<any>(Course.omoList, {
    method: 'post',
    body,
    transform: res => res.data,

  })
}

// 体验课列表
export async function experienceList (body: any) {
  return useHttp<any>(Course.experienceList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 图书列表
export async function bookList (body: any) {
  return useHttp<any>(Course.bookList, {
    method: 'post',
    body,
    transform: res => res.data,

  })
}

/**
 *
 * 0元商品
 *
 * @param body
 * @returns
*/
export async function get0YuanPage (body = {}) {
  return useHttp<any>('/kukecoregoods/wap/goods/getGoodsList', {
    key: '0_yuan',
    body: {
      ...body,
      listType: '0_yuan'
    },
    transform: res => res.data,
  })
}
/**
 *
 * 题库模块套餐
 *
 * @param body
 * @returns
*/
export async function getQuestionModulePackagePage (body = {}) {
  return useHttp<any>('/kukecoregoods/wap/goods/getGoodsList', {
    key: 'question_module',
    body: {
      ...body,
      listType: 'question'
    },
    transform: res => res.data,
  })
}
/**
 *
 * 商品分组
 *
 * @param body
 * @returns
*/
export async function getGroupListPage (body = {}) {
  return useHttp<any>('/kukecoregoods/wap/goods/getGoodsList', {
    key: 'group_list',
    body: {
      ...body,
      pageMasterId: '',
      // cateId: '', //
      listType: 'group_list'
    },
    transform: res => res.data,
  })
}
/**
 *
 * 分组id校验接口
 * groupId
 * @param body
 * @returns
*/
export async function checkGroupId (body = {}) {
  return useHttp<any>('/kukecoregoods/wap/goods/checkGroupId', {
    body,
    transform: res => res.data,
  })
}
/**
 * 全部商品
 *
 * @param body
 * @returns
*/
export async function getAllCoursePage (body = {}) {
  const _api = '/kukecoregoods/wap/goods/getGoodsList'
  const key = hash({
    ...body,
    _api
  })
  return useHttp<any>(_api, {
    key,
    body,
    transform: res => res.data,
  })
}
/**
 *
 * 获取专业分类下的标签
  * cateId 项目id
  * subjectId 科目ID
  * academicSectionId 学段ID
  * regionId 地区ID
  * examFormatId 考试形式ID
  * directionId 方向id
 * @param body
 * @returns
*/
export async function getCourseTags (body = {}) {
  const _api = '/kukecoregoods/wap/index/getTags'
  const key = hash({
    ...body,
    _api
  })
  return useHttp<any>(_api, {
    key,
    body,
    transform: res => res.data,
  })
}
/**
 * 获取商品全部专业分类
 *
 * @param body
 * @returns
*/
export async function getCourseCateIds (body = {}) {
  return useHttp<any>('/kukecoregoods/wap/index/getCateIds', {
    body,
    transform: res => res.data,
  })
}

// 收藏
export async function changeCollect (body: AddCollectParams) {
  return useHttp<void>(Course.changeCollect, {
    method: 'post',
    body
  })
}

// 获取规格信息
export async function getSpecsInfo (body: SpecsInfoParams) {
  return useHttp<GoodsSpecificationItem>(Course.getSpecsInfo, {
    method: 'post',
    body
  })
}

// 检查商品状态
export async function checkGoodsStatus (body: CheckGoodsParams) {
  return useHttp<CheckGoodsModel>(Course.checkGoodsStatus, {
    method: 'post',
    body
  })
}

// 获取营销活动信息
export async function getMarketingInfo (body: {
  goodsMasterId: string
  goodsSpecificationItemId?: string
  isBargain?: string
}) {
  return useHttp<ActivityData>(Course.marketingInfo, {
    method: 'post',
    body,
    watch: false
  })
}

// 绑定分销
export async function bindingDistributionRelation (body: {
  goodsMasterId: string
  superiorUserId: string
  goodsSpecificationItemId?: string
}) {
  return useHttp<any>(Course.bindingDistributionRelation, {
    method: 'post',
    body,
    server: false,
  })
}

// 记录分销生成海报次数
export async function distrpPosterRecord (body: {
  id: string
}) {
  return useHttp<any>(Course.distrpPosterRecord, {
    method: 'post',
    body
  })
}
// 获取面授列表
// omo列表
export async function offLineList (body: any) {
  return useHttp<any>(Course.offLineList, {
    method: 'post',
    body,
    transform: res => res.data,

  })
}

// 获取音频商品列表
export async function getAudioList (body: CollectionsListQuery) {
  return useHttp<CollectionResponse>(Course.audioList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取电子书商品列表
export async function getEbookList (body: CollectionsListQuery) {
  return useHttp<CollectionResponse>(Course.getEbookList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取合集商品列表
export async function getCollectionsList (body: CollectionsListQuery) {
  // 后端要求增加固定参数
  const temp = { ...body, hasFree: 1 }
  return useHttp<CollectionResponse>(Course.collectionsList, {
    method: 'post',
    body: temp,
    transform: res => res.data,
  })
}

// 获取合集商品详情列表
export async function getCollectionsDetail (body: CollectionsDetailQuery) {
  return useHttp<CollectionsDetailResponse>(Course.collectionsDetail, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true,
    watch: false
  })
}
// 官方力荐
export async function getOfficialRecommend (body: any) {
  return useHttp<any>(Course.getOfficialRecommend, {
    body,
    transform: res => res.data,
  })
}

// 获取商品的字段信息
export async function fetchGoodsFieldList (body: FetchGoodsFieldListParams) {
  return useHttp<FetchGoodsFieldListResponseData>(Course.fetchGoodsFieldList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 学管师信息
export async function getDistributeSuperviseTeacherInfoProt (body:TeacherParams) {
  return useHttp<TeacherResponseData>(Course.getDistributeSuperviseTeacherInfoProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 购物车多订单学管师信息
export async function getDistributeSuperviseTeacherListProt (body:TeacherParams) {
  return useHttp<TeacherResponseData>(Course.getDistributeSuperviseTeacherListProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 购物车是否包含学管师商品
export async function getIsServiceGoods (body:any) {
  return useHttp<any>(Course.isServiceGoodsV2, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 引流商品信息
export async function getAdGoods (body: any) {
  return useHttp<any>(Course.adGoods, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 购物车多订单引流图信息
export async function getAdGoodsByCart (body: any) {
  return useHttp<any>(Course.adGoodsByCart, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 商品购买按钮状态
export async function getBtnStatus (body:btnStatusParam) {
  return useHttp<btnStatusResponseData>(Course.getBtnStatus, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取小程序审核状态
export async function getMiniProgramStatus (body:any) {
  return useHttp<miniResponseData>(Course.getMiniProgramStatus, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 获取商品所有SKU组合数据（库存 SPU价格区间）
export async function getAllMultiSpecs (body:any) {
  return useHttp<SKUPrice>(Course.getAllMultiSpecs, {
    key: Course.getAllMultiSpecs + body?.goodsMasterId,
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 请求商品详情页配置
 * @param body
 * @returns
 */
export async function fetchGoodsDetailsPageConfig (body: FetchGoodsDetailsPageConfigParams) {
  return useHttp<FetchGoodsDetailsPageConfigDTO>(Course.fetchGoodsDetailsPageConfig, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 商品详情-课程阶段
 *
*/
export async function getCourseStage (body:any) {
  return useHttp<any>('/kukecoregoods/wap/goods/getCourseStage', {
    method: 'post',
    body,
    transform: res => res.data,
    // isShowLoading: true
  })
}
/**
 * 获取学员约课列表
 *
*/
export async function getYueKeList (body:any) {
  return useHttp<any>('/kukecoregoods/wap/userBuyUnitGoods/v1/yueKeListProt', {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
/**
 * c端约课学员预约
 *
*/
export async function userAppointmentAddProt (body:any) {
  return useHttp<any>('/kukestudentservice/wap/appointmentUser/userAppointmentAddProt', {
    method: 'post',
    body,
    // transform: res => res.data,
    isShowLoading: true
  })
}
/**
 * c端约课学员取消预约
 *
*/
export async function userCancelAppointmentProt (body:any) {
  return useHttp<any>('/kukestudentservice/wap/appointmentUser/userCancelAppointmentProt', {
    method: 'post',
    body,
    // transform: res => res.data,
    isShowLoading: true
  })
}
/**
 * c端签到
 *
*/
export async function userSignProt (body:any) {
  return useHttp<any>('/kukestudentservice/wap/appointmentUser/userSignProt', {
    method: 'post',
    body,
    // transform: res => res.data,
    isShowLoading: true
  })
}
/**
 * @description 根据商品id或权益id获取专业分类合标签信息
 *
 * @returns
 */
export async function getTagsByGoodsIdProt (body:FetchGoodsTagParamsType) {
  return useHttp<GoodsTagDTO>(Course.getTagsByGoodsIdProt, {
    method: 'post',
    body,
    transform: res => res.data
  })
}
/**
 * @description 根据商品权益相关属性获取专业分类和标签信息
 *
 * @returns
 */
export async function getTagsByGoodsRightProt (body = {}) {
  return useHttp<any>(Course.getTagsByGoodsRightProt, {
    method: 'post',
    body,
    transform: res => res.data
  })
}
/**
 * @description 热销课程
 *
 * @returns
 */
export async function hotGoodsList (body = {}) {
  const _api = Course.hotGoodsList
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    key,
    method: 'post',
    body,
    isShowLoading: true,
    transform: (res) => {
      const { data = {} } = res || {}
      const { list = [] } = data
      data.list = list.map((item: any) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { goodsProductList, teacherList = [], ...other } = item || {}
        return {
          ...other,
          teacherList: teacherList.map((v) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { briefIntroduction, joinedName, imagePhoto, ...o } = v || {}
            return o
          })
        }
      })
      return data
    }
  })
}
/**
 * @description 首页 - 体验课列表
 *
 * @returns
 */
export async function experienceListByHome (body = {}) {
  const _api = Course.experienceList
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    key,
    method: 'post',
    body,
    isShowLoading: true,
    transform: res => res.data
  })
}

/**
 * @description 查询拼团商品
 *
 * @returns
 */
export async function getGroupGoodsList (body = {}) {
  const _api = '/kukecoupon/wap/group/groupList'
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    key,
    method: 'post',
    body,
    isShowLoading: true,
    transform: res => res.data
  })
}
