import { events } from '@kukefe/kkutils'
export default () => {
  const {
    isXcx,
    isPc,
  } = useAppConfig()

  /**
     * @description 处理链接在不容容器下的跳转方式，
     * @param {*} url
     */
  const handleLinkJump = (url) => {
  // 如果是微信小程序
    if (isXcx && url.includes('work.weixin.qq.com/')) { return 'kefu' }
    if (isXcx) { return 'link' }

    if (isPc) {
      window.open(`https://${url}`)
    } else {
      location.href = `https://${url}`
    }
    return ''
  }

  /**
   * @description 发布事件
   *
   * @param {*} eventName 事件名
   * @param {*} data 数据源
   * @param {*} that
   * @returns
   */
  const handlePublish = (eventName, data, _that?:any) => {
    // const events = getEvents(_that)
    if (!events) { return }

    // 发布事件
    events.publish(eventName, data)
  }

  // 获取到事件名称
  const getEventName = (item) => {
    let eventName = 'page'
    /**
     * 在线客服
     */
    if (item.dicSkipId === 31 && item.customerServiceId) {
      eventName = 'onlineKefu'
    }
    /**
     * 商品分组
     */
    if (item.dicSkipId === 30 && item.groupId) {
      eventName = 'goodsGroup'
    }
    // 功能
    if (item.dicSkipId === 17) { eventName = 'page' }
    // 小程序
    if (item.dicSkipId === 19) { eventName = 'applet' }

    // TODO 如果是自定义页面
    if (item.dicSkipId === 29 && item.customPageId) {
      item.dicFunctionId = undefined
      eventName = 'customPage'
    }

    // 跳转类型是 链接并且存在值
    /* if (item.dicSkipId === 18 && item.url.includes('/material/detail/')) {
      eventName = 'materialDetail'
    } else */
    if (item.dicSkipId === 18 && item.url) {
      eventName = handleLinkJump(item.url)
    }
    if (item.dicSkipId === 20) {
      eventName = ''
    }

    return { eventName }
  }

  /**
   * 默认状态下发布事件机制
   */
  const handleDefaultPublish = (data, that?:any) => {
  // 事件名
    const { eventName } = getEventName(data.item)
    if (!eventName) { return }

    handlePublish(eventName, data, that)
  }
  return {
    handlePublish,
    handleDefaultPublish
  }
}
