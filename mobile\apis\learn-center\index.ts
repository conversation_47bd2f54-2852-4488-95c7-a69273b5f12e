export enum ApisLc {
  /** 获取用户课时统计信息 */
  getCourseStatistics = '/kukestudentservice/wap/kssUserCourseRecord/getUserCourseStatInfoProt',
  /** Wap端近30天看课总时长排行榜 */
  getCourseRankings = '/kukestudentservice/wap/kssUserCourseRecord/listWatchedTotalDurationProt',
  /** 获取用户近5天每天看课时长统计 */
  getCourseStudyTimeStatistics = '/kukestudentservice/wap/kssUserCourseRecord/listLearnedCourseDurationProt',
  /* Wap端根据传入的月份获取是否有直播课列表 */
  getLiveCourseByMonth = '/kukecoregoods/wap/kgUserBuyUnitGoods/listMyMonthLiveCourseProt',
  /* Wap端根据传入日期获取我的直播课列表 */
  getLiveCourseByDay = '/kukecoregoods/wap/kgUserBuyUnitGoods/listMyLiveCourseProt',
  /* Wap端获取课程信息和课时列表 */
  getLiveCourseDetail = '/kukecoregoods/wap/kgUserBuyUnitGoods/getMyCourseDetailProt',
  getLiveCourseDetailV2 = '/kukecoregoods/wap/kgUserBuyUnitGoods/getMyCourseDetailNewProt',
  /* 上传音频进度 */
  uploadAudioProgress = '/kukestudentservice/wap/userBroadcast/uploadAudioProgressProt',
  /* Wap端获取上次学习课程信息 */
  getLastLearnCourse = '/kukestudentservice/wap/kssUserCourseRecord/getLastLearnedCourseInfoProt',
  /* Wap端获取我的学习目标及课程类型 */
  // getLearnProjectList = '/kukestudentservice/wap/KssUserBuyUnitGoods/listMyLearnTargetAndTypeProt',
  // getLearnProjectList = '/kukecoregoods/wap/kgUserBuyUnitGoods/listMyLearnTargetAndTypeProt',
  getLearnProjectList = '/kukesearch/wap/kssUserBuyUnitGoods/v1/listMyLearnTargetAndTypeProt',
  /* Wap端获取我的套餐信息（我的班级） */
  getMyClassCourse = '/kukecoregoods/wap/kgUserBuyUnitGoods/getMyClassRoomInfoProt',
  /* Wap端获取我的套餐下课程列表 */
  // getClassList = '/kukecoregoods/wap/kgUserBuyUnitGoods/getMyClassRoomGoodsListProt',
  getClassList = '/kukecoregoods/wap/kgUserBuyUnitGoods/getMyClassRoomGoodsPageProt',
  /* Wap端获取我的全部班课 */
  // getAllClassList = '/kukestudentservice/wap/KssUserBuyUnitGoods/listMyOrderGoodsProt',
  // getAllClassList = '/kukecoregoods/wap/kgUserBuyUnitGoods/listMyOrderGoodsProt',
  getAllClassList = '/kukesearch/wap/kssUserBuyUnitGoods/v1/listMyOrderGoodsProt',
  /* Wap端切换置顶状态 */
  // toggleTopProt = '/kukestudentservice/wap/KssUserBuyUnitGoods/toggleTopProt',
  // toggleTopProt = '/kukecoregoods/wap/kgUserBuyUnitGoods/toggleTopProt',
  toggleTopProt = '/kukesearch/wap/kssUserBuyUnitGoods/v1/toggleTopProt',
  /* Wap端切换隐藏状态 */
  // 第一版
  // toggleHiddenProt = '/kukestudentservice/wap/KssUserBuyUnitGoods/toggleHiddenProt',
  // 第二版
  // toggleHiddenProt = '/kukecoregoods/wap/kgUserBuyUnitGoods/toggleHiddenProt',
  // 第三版
  toggleHiddenProt = '/kukesearch/wap/kssUserBuyUnitGoods/v1/toggleHiddenProt',
  /* Wap端校验用户购买的商品是否已过期 */
  checkGoodsExpire = '/kukestudentservice/wap/KssUserBuyUnitGoods/checkGoodsExpire',
  /* 校验主权益商品是否过期 */
  checkMainUnitGoodsExpire = '/kukestudentservice/wap/KssUserBuyUnitGoods/checkMainUnitGoodsExpire',
  /* 获取直播课频道信息 */
  getVideoChannel = '/kukecoregoods/wap/goods/getChannelInfo',
  /* 上传讲义学习进度 */
  uploadHandoutProgressProt = '/kukestudentservice/wap/userBroadcast/uploadHandoutProgressProt',
  /* wap端获取我的已学习时长 */
  sumMyLearnCourseDurationProt = '/kukestudentservice/wap/kssUserCourseRecord/sumMyLearnCourseDurationProt',
  /* Wap端课时扫码 */
  getCourseNode = '/kukecoregoods/wap/goods/courseNode',
  /* Wap端获取试听课列表 */
  tryListenOrdersProt = '/kukecoregoods/wap/index/tryListenOrdersProt',
  // 上传学习进度
  uploadProgressProt = '/kukestudentservice/wap/userBroadcast/uploadProgressProt',
  /* Wap端试听课置顶 */
  tryListenToppingProt = '/kukecoregoods/wap/index/tryListenToppingProt',
  /* Wap端试听课隐藏 */
  tryListenHideProt = '/kukecoregoods/wap/index/tryListenHideProt',
  /* Wap端试听课筛选 */
  tryListenSearchProt = '/kukecoregoods/wap/index/tryListenSearchProt',
  /* Wap端根据课时id，获取课时对应的商品主id */
  getGoodsMasterIdByCourseNodeId = '/kukecoregoods/goodsCourseNode/getGoodsMasterIdByCourseNodeId',
  // Wap 端获取学习记录
  studyRecordProt = '/kukecoregoods/wap/userBuyUnitGoods/studyRecordProt',

  /* 用户切换学习计划接口对接 */
  kgUserStudyPlanSelectionSwitchProt = '/kukecoregoods/wap/kgUserStudyPlanSelection/switchProt',
    /* 获取某一个学习阶段下的阶段详情 */
    getStudyPlanStageInfoProt = '/kukecoregoods/wap/kgUserBuyUnitGoods/getStudyPlanStageInfoProt',
  // C端资料包使用人数记录接口
  recordUserPort = '/kukestudentservice/wap/kssResourcePack/recordUserProt',
  /* Wap端判断是否有资料包接口 */
  checkIsResourceProt = '/kukestudentservice/wap/kssResourcePack/checkIsResourceProt',
  /* Wap端通过组织id获取播放器信息 */
  getPlayInfoProt = '/kukestudentservice/wap/userBroadcast/getPlayInfoProt',
}
/**
 *
 * @returns
 */
export async function kgUserStudyPlanSelectionSwitchProt (body = {}) {
  return useHttp<any>(ApisLc.kgUserStudyPlanSelectionSwitchProt, {
    body
  })
}
/**
 * 获取某一个学习阶段下的阶段详情
 * @param body
 * @returns
 */
export function getStudyPlanStageInfoProt (body: IBody = {}) {
  return useHttp<any>(ApisLc.getStudyPlanStageInfoProt, {
    method: 'post',
    body,
    transform: input => input.data
  })
}

export type IgetGoodsResourceType = {
  goodsMasterId:string
}

/**
 * 根据商品获取商品类型
 * @param body
 * @returns
 */
export function getGoodsResourceType (body: IgetGoodsResourceType) {
  return useHttp<any>('/kukecoregoods/wap/goods/getGoodsResourceType', {
    method: 'post',
    body,
    transform: input => input.data
  })
}
