import { BooleanEnum } from '../../types'

/**
 * 活动状态枚举
 */
export enum ActivityStatusEnum {
    Pending = 1, // 待开始
    InProgress = 2, // 进行中
    Completed = 3, // 已结束
}

/**
 * 启用状态枚举
 */
export enum EnableStatusEnum {
    Disable = 0, // 未启用
    Enable = 1, // 已启用
}

/**
 * 活动详情 DTO
 */

export interface FetchActivityDetailDTO {
    activityName: string; // 活动名称（非必须）
    activityExplain: string; // 活动说明（必须）
    posterPc: string; // PC海报（必须）
    posterApp: string; // APP海报（必须）
    isEnable: EnableStatusEnum; // 是否启用
    status: ActivityStatusEnum; // 活动状态
    activityButtonName: string;// 活动按钮名称
    buttonTextColor: string;// 按钮文字颜色
    buttonBackgroundColor: string;// 按钮背景颜色
}

/**
 * 申请条件 枚举
 */
export enum ApplicationRequirementTypeEnum {
    NotRequired = 0, // 无条件
    Required = 1, // 有条件
}

/**
 * 申请信息 枚举
 */
export enum ApplicationInformationTypeEnum {
    NotRequired = 0, // 不需要
    Required = 1, // 需要
}

/**
 * 购买类型 枚举
 */
export enum BuyTypeEnum {
    ALL = 1, // 购买任意商品
    Assign = 2, // 购买指定商品
}

/**
 * 审核类型 枚举
 */
export enum AuditStatusEnum {
    PendingApply = 0, // 用户待申请
    PendingAudit = 1, // 待审核
    AuditPass = 2, // 审核通过
    AuditFail = 3, // 审核失败
}

export interface OptionVO {
    option: number;
    optionName: string;
}

/**
 * 字段类型枚举
 */
export enum FieldTypeEnum {
    Text = 1, // 文本类型
    Select = 2, // 下拉选择
}

/**
 * 推广内容类型枚举
 */
export enum ExtensionTypeEnum {
    Product = 1, // 推广商品
    Coupon = 2, // 推广优惠券
}

/**
 * 填写信息 VO
 */
export interface ApplicationFillVO {
    fieldType: FieldTypeEnum; // 字段类型 1 文本类型 2下拉选择
    fieldName: string; // 字段名称
    isMust: BooleanEnum; // 是否必填 1是 0否
    options: OptionVO[],
}

/**
 * 申请活动详情 DTO
 */
export interface FetchApplyActivityDetailDTO {
    applicationRequirementType: ApplicationRequirementTypeEnum; // 申请条件
    applicationInformationType: ApplicationInformationTypeEnum;// 申请信息是否需要
    applicationFillList: ApplicationFillVO[], // 填写信息
    buyType: BuyTypeEnum; // 购买类型
    passMuster: boolean; // 是否符合要求
    goodsList: any[]; // 商品信息（必须）待完善
    auditStatus: AuditStatusEnum; // 审核状态
    auditOpinion: string; // 审批意见（必须）
    haveAuditRecord: boolean;// 是否有审核记录
    isAudit:BooleanEnum;// 是否需要审核
}

/**
 * 请求提交申请 参数
 */
export interface FetchSubmitApplyParams {
    id: string,
    applicationFillList?: {
        fieldName: string;// 字段名称
        fieldContent: string;// 字段内容
    }[]
}

/**
 * 活动简单字段VO
 */
export interface ActivitySimpleVO {
    /** 活动ID */
    id: number;

    /** 活动名称 */
    activityName: string;

    /** 推广类型 */
    extensionType: ExtensionTypeEnum;
}

/**
 * 数据传输对象，用于获取用户推广信息
 */
export interface FetchUserPromoterInfoDTO {
    /** 用户名 */
    userName: string;

    /** 头像 */
    photo: string;

    /** 推广等级名 */
    extensionGradeName: string;

    /** 提现规则 */
    withdrawalRule: string;

    /** 可提现金额 */
    withdrawalAmount: number;

    /** 不可提现金额 */
    notWithdrawalAmount: number;

    /** 推广用户数 */
    extensionUserSum: number;

    /** 推广订单数 */
    extensionOrderSum: number;

    /** 退款结算天数 */
    orderCantApplyRefundDays:number;

    activityList: ActivitySimpleVO[];
}

/**
 * 收益列表-请求参数
 */
export interface FetchIncomeListParams {
    /** 页码 */
    page: number; // 必须字段

    /** 每页条数 */
    pageSize: number; // 必须字段
}

/**
 * 收益列表数据传输对象
 */
export interface FetchIncomeListDTO {
    /** 用户昵称 */
    userName: string;

    /** 用户头像 */
    photo: string;

    /** 下单时间 */
    orderTime: string;

    /** 订单状态 */
    orderStatus: string;

    /** 订单状态翻译 */
    orderStatusName: string;

    /** 佣金 */
    amount: string;

    /** 是否新用户，0-否，1-新用户 */
    newUser: string;

    /** 是否可提现，0-不可提现，1-可提现 */
    withdrawal: number;

    /** 商品名称 */
    goodsTitle: string;

    /** 备注 */
    remark: string;
}

/**
 * 优惠券列表-请求参数
 */
export interface FetchCouponListParams {
    /** 页码 */
    page: number; // 必须字段

    /** 每页条数 */
    pageSize: number; // 必须字段

    id: string; // 活动id
}

/**
 * 商品列表-请求参数
 */

export interface FetchGoodsListParams {
    /** 页码 */
    page: number; // 必须字段

    /** 每页条数 */
    pageSize: number; // 必须字段

    id: string; // 活动id

    goodsTitle:string;// 商品名称
}
