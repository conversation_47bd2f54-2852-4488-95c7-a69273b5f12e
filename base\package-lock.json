{"name": "base", "version": "1.2.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "base", "version": "1.2.0", "dependencies": {"crypto-js": "^4.1.1", "md5": "^2.3.0", "node-forge": "^1.3.1"}, "devDependencies": {"nuxt": "3.7.4"}}, "../node_modules/.pnpm/crypto-js@4.1.1/node_modules/crypto-js": {"version": "4.1.1", "license": "MIT"}, "../node_modules/.pnpm/md5@2.3.0/node_modules/md5": {"version": "2.3.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": "0.0.2", "crypt": "0.0.2", "is-buffer": "~1.1.6"}, "devDependencies": {"mocha": "~2.3.4", "webpack": "~2.4.1"}}, "../node_modules/.pnpm/node-forge@1.3.1/node_modules/node-forge": {"version": "1.3.1", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "devDependencies": {"browserify": "^16.5.2", "commander": "^2.20.0", "cross-env": "^5.2.1", "eslint": "^7.27.0", "eslint-config-digitalbazaar": "^2.8.0", "express": "^4.16.2", "karma": "^4.4.1", "karma-browserify": "^7.0.0", "karma-chrome-launcher": "^3.1.0", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^1.3.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-sourcemap-loader": "^0.3.8", "karma-tap-reporter": "0.0.6", "karma-webpack": "^4.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "nodejs-websocket": "^1.7.1", "nyc": "^15.1.0", "opts": "^1.2.7", "webpack": "^4.44.1", "webpack-cli": "^3.3.12", "worker-loader": "^2.0.0"}, "engines": {"node": ">= 6.13.0"}}, "../node_modules/.pnpm/nuxt@3.7.4_@types+node@18.17.15_eslint@8.49.0_sass@1.66.1_terser@5.24.0_typescript@5.1.6/node_modules/nuxt": {"version": "3.7.4", "dev": true, "license": "MIT", "dependencies": {"@nuxt/devalue": "^2.0.2", "@nuxt/kit": "3.7.4", "@nuxt/schema": "3.7.4", "@nuxt/telemetry": "^2.5.0", "@nuxt/ui-templates": "^1.3.1", "@nuxt/vite-builder": "3.7.4", "@unhead/dom": "^1.7.4", "@unhead/ssr": "^1.7.4", "@unhead/vue": "^1.7.4", "@vue/shared": "^3.3.4", "acorn": "8.10.0", "c12": "^1.4.2", "chokidar": "^3.5.3", "cookie-es": "^1.0.0", "defu": "^6.1.2", "destr": "^2.0.1", "devalue": "^4.3.2", "esbuild": "^0.19.3", "escape-string-regexp": "^5.0.0", "estree-walker": "^3.0.3", "fs-extra": "^11.1.1", "globby": "^13.2.2", "h3": "^1.8.1", "hookable": "^5.5.3", "jiti": "^1.20.0", "klona": "^2.0.6", "knitwork": "^1.0.0", "magic-string": "^0.30.3", "mlly": "^1.4.2", "nitropack": "^2.6.3", "nuxi": "^3.9.0", "nypm": "^0.3.3", "ofetch": "^1.3.3", "ohash": "^1.1.3", "pathe": "^1.1.1", "perfect-debounce": "^1.0.0", "pkg-types": "^1.0.3", "radix3": "^1.1.0", "scule": "^1.0.0", "std-env": "^3.4.3", "strip-literal": "^1.3.0", "ufo": "^1.3.0", "ultrahtml": "^1.5.2", "uncrypto": "^0.1.3", "unctx": "^2.3.1", "unenv": "^1.7.4", "unimport": "^3.3.0", "unplugin": "^1.5.0", "unplugin-vue-router": "^0.7.0", "untyped": "^1.4.0", "vue": "^3.3.4", "vue-bundle-renderer": "^2.0.0", "vue-devtools-stub": "^0.1.0", "vue-router": "^4.2.5"}, "bin": {"nuxi": "bin/nuxt.mjs", "nuxt": "bin/nuxt.mjs"}, "devDependencies": {"@parcel/watcher": "2.3.0", "@types/estree": "1.0.2", "@types/fs-extra": "11.0.2", "@vitejs/plugin-vue": "4.3.4", "unbuild": "latest", "vite": "4.4.9", "vitest": "0.33.0"}, "engines": {"node": "^14.18.0 || >=16.10.0"}, "peerDependencies": {"@parcel/watcher": "^2.1.0", "@types/node": "^14.18.0 || >=16.10.0"}, "peerDependenciesMeta": {"@parcel/watcher": {"optional": true}, "@types/node": {"optional": true}}}, "node_modules/crypto-js": {"resolved": "../node_modules/.pnpm/crypto-js@4.1.1/node_modules/crypto-js", "link": true}, "node_modules/md5": {"resolved": "../node_modules/.pnpm/md5@2.3.0/node_modules/md5", "link": true}, "node_modules/node-forge": {"resolved": "../node_modules/.pnpm/node-forge@1.3.1/node_modules/node-forge", "link": true}, "node_modules/nuxt": {"resolved": "../node_modules/.pnpm/nuxt@3.7.4_@types+node@18.17.15_eslint@8.49.0_sass@1.66.1_terser@5.24.0_typescript@5.1.6/node_modules/nuxt", "link": true}}}