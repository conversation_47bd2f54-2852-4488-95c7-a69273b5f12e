// import { consola } from 'consola'
const errorList_ = [
  'error loading dynamically imported module',
  'Importing a module script failed',
  'Failed to fetch dynamically imported module',
]
export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.hook('vue:error', (err, target, info) => {
    console.log('vue:error:')
    console.error(err, target, info)
    errorHandle(err)
  })
  nuxtApp.hook('app:error', (error) => {
    console.log('app:error')
    console.error(error)
    errorHandle(error)
  })
  nuxtApp.vueApp.config.errorHandler = (..._args) => {
    console.log('[base] global error handler:')
    console.error(..._args)
  }
  /**
 * @description 监听路由错误，正则匹配对应的错误消息后，自动刷新界面
 * 解决前端发版出现-跳转路由页面加载该页面js文件404时，触发router.onError的场景
 */
  nuxtApp.$router?.onError((error) => {
    errorHandle(error)
  })
})

const errorHandle = (error: { message: string }) => {
  if (process.client) {
    const msg = error?.message
    for (const message of errorList_) {
      if (msg?.includes(message)) {
        Message('网络开小差了，请刷新重试～')
      }
    }
  }
}
