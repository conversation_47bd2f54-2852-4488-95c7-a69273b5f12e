enum MayDayApi {
  interface = '/kukeactivity/marketing0909/index',
  apply = '/kukeactivity/marketing0909/apply',
}
// 报名抽奖
export async function MayDayApply () {
  return useHttp<any>(MayDayApi.apply, {
    method: 'post',
    watch: false,
    transform: res => res.data,
  })
}

// 活动界面
export async function MayDayInterface (body: any) {
  return useHttp<any>(MayDayApi.interface, {
    method: 'post',
    body,
    watch: false,
    transform: res => res,
  })
}
