import type { VideoDetailType } from '../components/QuestionVideoPlayer/types'
import { getQuestionVideoInfo } from '../apis/video'
export const useQuestionVideoInfo = () => {
  const videoInfo: VideoDetailType = reactive({
    ts: '',
    sign: '',
    playSafe: '',
    siteId: '',
    ccFlag: 0,
    videoId: '',
    questionId: '',
    videoType: 1,
    orgId: ''
  })

  // 获取视频信息
  const getVideoInfo = async (questionId: string) => {
    try {
      const { data, error } = await getQuestionVideoInfo({ questionId })
      if (!error.value) {
        console.log('获取视频信息成功', data.value)
        const testData = {
          // 用来测试
          // videoType: 2,
          // videoId: '737AC5ED68E2A08F753C612EB38A8D5A',
          // siteId: 'FE6E68B94C044B45'
        }
        Object.assign(videoInfo, data.value.data, testData)
        return Promise.resolve()
      } else {
        Message(error.value.message)
        return Promise.reject(error)
      }
    } catch (error) {
      console.log('获取视频信息失败', error)
      return Promise.reject(error)
    }
  }

  return {
    videoInfo,
    getVideoInfo,
  }
}
