<template>
  <button
    v-if="nativeType"
    v-bind="$attrs"
    :type="nativeType"
    :class="classStyle"
    :disabled="disabledOk"
    :style="{ width: btnWidth, background: bgColor, color, height, border, fontSize, fontWeight }"
    @click.prevent="handleClick"
  >
    <KKCIcon
      v-if="icon2"
      :name="`icon-${icon2}`"
      :size="iconSize"
    />
    <slot />
  </button>
  <a
    v-else
    v-bind="$attrs"
    :class="classStyle"
    :href="disabledOk ? null : routerHref"
    :style="{ width: btnWidth }"
    @click="handleClick"
  >
    <KKCIcon
      v-if="icon2"
      :name="`icon-${icon2}`"
      :size="iconSize"
      hover-color="var(--kkc-red)"
    />
    <slot />
  </a>
</template>

<script lang="ts" setup>
import { computed, inject, ref } from 'vue'

export interface GroupPropConfig {
  disabled?: boolean
  round?: boolean
  size?: string
  width?: string
  plain?: boolean
}

const props = withDefaults(
  defineProps<{
    type?: 'primary'|'info'|'danger'|''
    size?: 'large'|'middle'|'tiny'|'small'|'normal' | undefined
    href?: string
    icon?: string
    round?: boolean
    disabled?: boolean
    nativeType?: '' | 'button' | 'submit' | 'reset'
    width?: string
    name?: string // btn group中作为唯一标识
    loading?: boolean // 是否加
    plain?: boolean // 是否为朴素按钮
    circle?: boolean // 是否为圆形
    iconSize?: number
    bgColor?: string
    color?: string
    height?: string,
    border?:string
    fontSize?:string
    fontWeight?:number
  }>(),
  {
    type: '',
    size: '',
    href: '',
    icon: '',
    round: false,
    disabled: false,
    nativeType: 'button',
    width: '',
    name: '', // btn group中作为唯一标识
    loading: false, // 是否加
    iconSize: 24,
    bgColor: '',
    border: 'none',
    fontSize: '',
  }
)
const emits = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

const btnClick: any = inject('kkcBtnClick', '')
const disabledOk = computed(() => {
  if (props.loading) {
    return true // loading情况下一定为禁用状态
  } else {
    return getFormDisabled(props.disabled) || false
  }
})
const btnWidth = ref(props.width)
const classStyle = computed(() => {
  const size = props.size || ''
  return {
    'kkc-btn': true,
    'is-round': props.round,
    ['kkc-btn-' + props.type]: props.type,
    disabled: disabledOk.value,
    [size]: size,
    'is-plain': props.plain,
    'is-circle': props.circle,
  }
})
const routerHref = computed(() => {
  // 解释路由url，即<router-link :to="/button">转<a href="#/button">
  if (props.href) {
    return props.href
  } else {
    return null
  }
})
const handleClick = (event: MouseEvent) => {
  if (!disabledOk.value) {
    emits('click', event)
    btnClick && btnClick(event, props.name)
  }
}
const icon2 = computed(() => {
  // 使用加载状态的icon
  return props.loading ? 'loading' : props.icon
})
</script>
