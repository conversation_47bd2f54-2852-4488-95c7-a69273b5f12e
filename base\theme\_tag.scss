@mixin tagLighten($color, $num) {
  /* border: 1px solid lighten($color, $num); */  
  background-color: rgba($color, $num);
  color: $color;
}

@include ns(tag) {
  @include tagLighten($primaryTagColor, 0.1);
  display: inline-block;
  height: 20px;
  padding: 0 6px;
  line-height: 20px;
  font-size: 11px;
  border-radius: 6px;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: center;

  +.#{$namespace}tag {
    margin-left: 6px;
  }

  /* .icon-close {
    font-size: 12px;
    width: 16px;
    height: 16px;
    display: inline-block;
    margin-left: 10px;
    transform: scale(0.8);
    vertical-align: middle;
    line-height: 16px;
    cursor: pointer;
    &:hover {
      opacity: 0.6;
    }
  } */
  &.tag-success {
    @include tagLighten($successTagColor, 0.1);
  }

  &.tag-warning {
    @include tagLighten($warningTagColor, 0.1);
  }

  &.tag-danger {
    @include tagLighten($dangerTagColor, 0.1);
  }

  &.tag-large {
    height: 32px;
    padding: 0 10px;
    line-height: 32px;
    font-size: 14px;
    font-weight: 400;
  }

  &.tag-middle {
    height: 28px;
    padding: 0 8px;
    line-height: 28px;
    font-size: 18px;
    font-weight: 500;
  }

  &.tag-small {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    font-weight: 500;
  }

  &.tag-mini {
    height: 18px;
    line-height: 18px;
    padding: 0 4px;
    font-size: 10px;
    font-weight: 500;
    border-radius: 4px;
  }
}