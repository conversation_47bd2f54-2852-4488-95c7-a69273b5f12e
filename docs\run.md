
### `打包`及`部署镜像`环境要求

```bash
node >=18.18.2
pnpm >=7
pm2 * # 可选
```

### 修改本地 .env PRODUCT_TYPE_DOMAIN(产品线域名)


```sh
# 产品线: 库课网校
# pc 
PRODUCT_TYPE_DOMAIN = kukeyun-devc.tyyk99.com
PRODUCT_TYPE_DOMAIN = m-kukeyun-devc.tyyk99.com

```
### 打包部署流程

[生产环境上线步骤（以此文档为准）](https://alidocs.dingtalk.com/i/nodes/MNDoBb60VLrMknQef3gpjpmR8lemrZQ3?utm_scene=team_space)

## git提交

```
npm i -g commitizen@4.2.4
git add [/any/path]
git cz
```

### 安装

```bash
# 1.
pnpm install
# pnpm install --frozen-lockfile
# 2.指定版本(可选)
pnpm add @kukejs/business-wap@x.x.x -C mobile
pnpm add @kukejs/business-web@x.x.x -C pc
# 3. 更新 iconfont (可选)
pc 上传地址 https://oss.kuke99.com/kukecloud/pc/iconfont.js
mobile 上传地址 https://oss.kuke99.com/kukecloud/mobile/iconfont.js
```

### 0. 运行

```bash

# 开发环境
pnpm run dev:pc # preview http://localhost:3000
pnpm run dev:m # preview http://localhost:3006

# 测试环境
pnpm run dev:pc:staging # preview http://localhost:3000
pnpm run dev:m:staging # preview http://localhost:3006

```

### 1. 打包:

```bash

## 正式环境
http-proxy-prefix /prod-api
# mobile
pnpm run build:m:prod # pnpm -C mobile build
# pc
pnpm run build:pc:prod # pnpm -C pc build

# 开发环境 dev 
http-proxy-prefix /dev
# mobile
pnpm run build:m:dev # pnpm -C mobile build:dev
# pc
pnpm run build:pc:dev # pnpm -C pc build:dev

# 测试环境 staging

http-proxy-prefix /stage-api
# mobile
pnpm run build:m:staging # pnpm -C mobile build:staging
# pc
pnpm run build:pc:staging # pnpm -C pc build:staging

# 压测环境 pressure

http-proxy-prefix /pressure-api
# mobile
pnpm run build:m:pressure # pnpm -C mobile build:pressure
# pc
pnpm run build:pc:pressure # pnpm -C pc build:pressure

# 预发布环境 pre

http-proxy-prefix /pre-api
# mobile
pnpm run build:m:pre # pnpm -C mobile build:pre
# pc
pnpm run build:pc:pre # pnpm -C pc build:pre

```