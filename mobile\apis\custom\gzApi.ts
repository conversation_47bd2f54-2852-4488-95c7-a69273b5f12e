import type { SaveExamTarget } from './gzTypes'

// 定制化的接口
enum CustomApi {
  saveExamTargetProt = '/kukecoreuser/wap/exam/saveExamTargetProt', // 获取专业分类列表
  checkExamTargetProt = '/kukecoreuser/wap/exam/checkExamTargetProt' // 是否填写了报考目标

}

// 保存公职的报考目标
export async function saveExamTargetProt (body: SaveExamTarget) {
  return useHttp<void>(CustomApi.saveExamTargetProt, {
    method: 'post',
    body,
  })
}

// 是否填写了报考目标
export async function checkExamTargetProt () {
  return useHttp<void>(CustomApi.checkExamTargetProt, {
    method: 'post',
  })
}
