export const base642File = (base64Str:string, fileName = '海报') => {
  const arr = base64Str.split(',')
  const mime = arr[0].match(/:(.*?);/)[1] // base64解析出来的图片类型
  const bstr = atob(arr[1])
  let len = bstr.length
  const ab = new ArrayBuffer(len) // 将ASCII码小于0的转换为大于0
  const u8arr = new Uint8Array(ab) //
  while (len--) {
    u8arr[len] = bstr.charCodeAt(len)
  }
  return new File([u8arr], fileName, {
    type: mime
  })
}
