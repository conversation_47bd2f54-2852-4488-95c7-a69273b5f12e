declare interface ILearnTargetNode {
  catalogueCode?: string;
  id?: string;
  usedId: string;
  parentId?: string;
  categoryName: string;
  pageMasterId: string;
  hasChildren: boolean;
  aliasType:string;
  alias?: string;
  level: number;
  cateId: number;
  children?: ILearnTargetNode[];
  subcategoryList?:ILearnTargetNode[],
  qiYuGroupId?:string;
}

declare interface NUXT_ENV_CONFIG {
  NUXT_BUILD_ENV: 'LOCAL' | 'LOCAL_STAGING' | 'DEV' | 'STAGING' | 'PRESSURE' | 'PRE' | 'PROD';
}