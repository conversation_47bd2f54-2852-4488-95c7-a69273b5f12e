import { PAGE_IDS_COURSE_LIST } from '../../mobile/constants' // 需要 pc、m端 PAGE_IDS_COURSE_LIST 融合到 /base/

interface Options {
  type?:'POPUP' | 'LAUNCH'
  // StorageKey?:string
}

/**
 *
 * @param param0
 * @returns
 */
export function useAdCountdownRAF (opts:Options) {
  const route = useRoute()
  const visible = ref(false)
  const timer = ref()
  const timeText = ref(0)
  const POPUP__1 = 'POPUP__1'
  const { setItem, getItem } = useLocalStorage()
  function getPageId () {
    if (PAGE_IDS_COURSE_LIST.includes(Number(route.meta?.pageid))) {
      return PAGE_IDS_COURSE_LIST[0]
    } else {
      return route.meta?.pageid
    }
  }
  function close (cb?:()=>void) {
    visible.value = false
    cb && cb()
  }
  function cancelTimer () {
    window.cancelAnimationFrame(timer.value)
  }
  const endTime = ref()
  function step () {
    const diff = endTime.value - Date.now()
    if (diff > 0) {
      const s = Math.floor(diff / 1000) // round
      // console.log(`${s} S`)
      timeText.value = s
      timer.value = window.requestAnimationFrame(step)
    } else {
      window.cancelAnimationFrame(timer.value)
      close()
    }
  }
  const trigger = (key:string, dwellSeconds?:Ref<number>) => {
    if (opts.type === 'POPUP') {
      setPopupValue(key)
      visible.value = true
    } else if (opts.type === 'LAUNCH') {
      // setLaunchValue(key)
      visible.value = true
    }
    if (visible.value && unref(dwellSeconds)) {
      endTime.value = Date.now() + unref(dwellSeconds) * 1000
      timer.value = window.requestAnimationFrame(step)
    }
  }

  const setPopupValue = (key:string) => {
    const pageid = getPageId()
    const list = getItem(key) || []
    // eslint-disable-next-line eqeqeq
    const i = list?.findIndex(v => v == pageid)
    if (i === -1) {
      list.push(pageid)
      setItem(key, list)
    } else {
      //
    }
  }
  // const setLaunchValue = (key:string) => {
  //   const pageid = getPageId()
  //   const list = JSON.parse(sessionStorage.getItem(key) || 'null') || []
  //   // eslint-disable-next-line eqeqeq
  //   const i = list?.findIndex(v => v == pageid)
  //   if (i === -1) {
  //     list.push(pageid)
  //     sessionStorage.setItem(key, JSON.stringify(list))
  //   } else {
  //     //
  //   }
  // }
  onMounted(() => {
    //
  })
  return {
    POPUP__1,
    visible,
    timer,
    cancelTimer,
    close,
    trigger,
    // endTime,
    timeText,
    getPageId,
  }
}
interface Record {
  value: any;
  expiration?: number;
}

export function useLocalStorage () {
  function setItem (key: string, value: any, expirationTime?: number): void {
    if (expirationTime && String(expirationTime).length !== 13) {
      console.error('[expirationTime] length must be 13')
    }
    const record: Record = {
      value,
      expiration: expirationTime
    }
    localStorage.setItem(key, JSON.stringify(record))
  }

  function getItem (key: string): any {
    const record: Record | null = JSON.parse(localStorage.getItem(key) || 'null')
    if (!record) {
      return null
    }
    if (record.expiration && new Date().getTime() > record.expiration) {
      localStorage.removeItem(key)
      return null
    }
    return record.value
  }

  function removeItem (key: string): void {
    localStorage.removeItem(key)
  }

  function clear (): void {
    localStorage.clear()
  }

  return {
    setItem,
    getItem,
    removeItem,
    clear
  }
}

export function getNextDayStartTimeDate (n = 1):Date {
  // 获取当前时间戳
  const currentTimeStamp = Date.now()

  // 创建一个日期对象
  const currentDate = new Date(currentTimeStamp)

  // 将时间设置为0点
  currentDate.setHours(0, 0, 0, 0)

  // 增加一天
  currentDate.setDate(currentDate.getDate() + (n || 0))

  // 获取下一天的0点的时间戳
  // const nextDayTimeStamp = currentDate.getTime()

  return currentDate
}
