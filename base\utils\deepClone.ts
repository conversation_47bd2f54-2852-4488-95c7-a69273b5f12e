/**
 * 递归克隆对象
 * @param obj 要克隆的对象
 * @param cache 缓存对象，用于存储已克隆的对象，避免重复克隆
 * @return 克隆后的对象
 */
export function deepClone<T> (obj: T, cache: WeakMap<object, object> = new WeakMap()): T {
  // 如果对象为null，则返回null
  if (obj === null) { return null }
  // 如果对象不是对象，则直接返回
  if (typeof obj !== 'object') { return obj }

  // 如果缓存中有该对象，则直接返回缓存中的克隆
  if (cache.has(obj)) {
    return cache.get(obj) as T
  }

  // 创建一个新的对象，如果是数组，则创建一个空数组，如果不是，则创建一个空对象
  const newObj: any = Array.isArray(obj) ? [] : {}
  // 将新对象添加到缓存中
  cache.set(obj, newObj)

  // 如果是数组，则遍历数组，并将每个元素克隆
  if (Array.isArray(obj)) {
    for (let i = 0; i < obj.length; i++) {
      newObj[i] = deepClone(obj[i], cache)
    }
  } else {
    // 如果是对象，则遍历对象的属性，并将每个属性克隆
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        newObj[key] = deepClone(obj[key], cache)
      }
    }
  }

  // 返回新对象
  return newObj
}
