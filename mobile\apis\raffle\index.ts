import type { ClaimInformation, userLotteryRes, raffleRes, MyAwardRecordRes } from './types'

enum raffle {
  lotteryInterface = '/kukecoupon/wap/lottery/lotteryInterface',
  userClaimInformation = '/kukecoupon/wap/lottery/userClaimInformation',
  userLotteryRecord = '/kukecoupon/wap/lottery/getUserLotteryRecord',
  shareLottery = '/kukecoupon/wap/lottery/shareLottery',
  userLottery = '/kukecoupon/wap/lottery/userLottery',
  prizeDeliveryInfo = '/kukecoupon/wap/lottery/prizeDeliveryInfo',
  lotteryShareDetails = '/kukecoupon/wap/lottery/lotteryShareDetails'
}

/*
 *查询可用优惠券及活动
*/
export async function getRaffleDetail (body: { id: string }) {
  return useHttp<raffleRes>(raffle.lotteryInterface, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/*
 *领奖信息填写
*/
export async function userClaimInformation (body: ClaimInformation) {
  return useHttp<any>(raffle.userClaimInformation, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/*
 *获取用户中奖记录
*/
export async function getUserLotteryRecord (body: { id: string }) {
  return useHttp<MyAwardRecordRes[]>(raffle.userLotteryRecord, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/*
 *分享抽奖次数加一
*/
export async function handleShareLottery (body: { id: string, userId: string }) {
  return useHttp<any>(raffle.shareLottery, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/*
 *用户抽奖
*/
export async function handleUserLottery (body: { lotteryId: string, questionLotteryId?: string }) {
  return useHttp<userLotteryRes>(raffle.userLottery, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/*
 *中奖填写信息回显
*/
export async function prizeDeliveryInfo (body: { id: string }) {
  return useHttp<any>(raffle.prizeDeliveryInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/*
 *分享详情
*/
export async function doLotteryShareDetails (body: { id: string }) {
  return useHttp<any>(raffle.lotteryShareDetails, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
