<script setup lang="ts">
import { computed } from 'vue'

interface PropsType {
  list: any[] // 抽奖数据列表
  id: string // 中将id
  initSpeed?: number // 抽奖转动速度
  fastSpeed?: number // 抽奖最快转动速度
  slowSpeed?: number // 抽奖最慢转动速度
  baseCircles?: number // 基础圈数
}
const props = withDefaults(defineProps<PropsType>(), {
  initSpeed: 150,
  fastSpeed: 50,
  slowSpeed: 500,
  baseCircles: 4,
})
const emits = defineEmits<{
  (e: 'end'): void,
  (e: 'start'): void
}>()

const { list, fastSpeed, slowSpeed, initSpeed } = toRefs(props)

// 转动的
const state = ref({
  curIndex: -1, // 当前位置
  curStep: 0, // 已跑圈数
  speed: 100, // 转动速度
  isRunning: false, // 是否正在抽奖
})
const _state = toRaw({ ...state.value })
const timer = ref<any>()
const lotterySort = ref([0, 1, 2, 5, 8, 7, 6, 3])

const addIndex = (arr: any[], i: number) => {
  switch (i) {
    case 0:
    case 1:
    case 2:
      arr[i].index = i + 1
      break
    case 3:
      arr[i].index = 8
      break
    case 4:
      arr[i].index = 9
      break
    case 5:
      arr[i].index = 4
      break
    case 6:
      arr[i].index = 7
      break
    case 7:
      arr[i].index = 6
      break
    case 8:
      arr[i].index = 5
      break
    default:
      console.log('default')
  }
  return arr
}

// 九宫格数据改造
// eslint-disable-next-line vue/return-in-computed-property
const lotteryList = computed(() => {
  if (process.client) {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    list.value.splice(4, 0, { index: 9 })
    console.log('AAAA', list.value)

    let arr: any[] = []
    for (let i = 0; i < 9; i++) {
      if (list.value[i]) {
        arr.push(list.value[i])
        arr = addIndex(arr, i)
      }
    }
    return arr
  }
})

const winId = ref()

watch(() => props.id, (nv) => {
  if (!nv) {
    const noIdArr = lotteryList.value.filter((item, idx) => {
      return item.id === undefined && idx !== 4
    })
    // 随机选择一个没有中奖的一项
    const randomIdx = Math.floor(Math.random() * noIdArr.length)
    winId.value = noIdArr[randomIdx]?.index || ''
  } else {
    const item = lotteryList.value.find((e) => {
      return e.id === nv
    })
    winId.value = item.index
    console.log(lotteryList.value)
  }
  console.log('🚀 ~ watch ~ winId.value:', winId.value)
})

// 获取当前的目标index
// const winId = computed(() => {
//   // console.log('~~id~~', id.value)
//   console.log('~~propsid~', props)
// })

// 获取总共跑得步数
const totalSteps = computed(() => {
  console.log('aaa', lotterySort.value.indexOf(winId.value) + 1)

  return props.baseCircles * 8 + (winId.value || 1)
})

// 需要加速的前段步数
const frontSteps = computed(() => {
  return Math.floor(props.baseCircles * 8 * (1 / 3))
})

// 需要减速的前段步数
const midSteps = computed(() => {
  return Math.floor(props.baseCircles * 8 * (5 / 6))
})

/**
 * 计算速度
 * @param  speed 速度
 * @return  speed
 **/
const calcSpeed = (speed: number) => {
  // 前段加速，后段减速
  if (state.value.curStep < frontSteps.value && speed > fastSpeed.value) {
    speed = speed - 10
  } else if (state.value.curStep > midSteps.value && speed < slowSpeed.value) {
    // 减速不一定要减速到最慢速度，优先保证动画效果看着协调
    speed = speed + 25
  } else {
    console.log('没有减速')
  }
  return speed
}

/**
 *开始跑
 **/
const startRun = () => {
  // 延时器的速度要动态调节
  timer.value && clearTimeout(timer.value)
  // console.log(`已走步数=${state.curStep}, 执行总步数=${totalSteps.value}`);
  // 已走步数超过要执行总步数, 则停止
  if (state.value.curStep >= totalSteps.value) {
    state.value.isRunning = false
    if (!winId.value) {
      state.value.curIndex = 4
    }
    return emits('end')
  }
  // 高亮抽奖格子序号
  state.value.curIndex = lotterySort.value[state.value.curStep % 8]
  // console.log('🚀 ~ startRun ~ lotterySort.value[state.value.curStep % 8]:', lotterySort.value[state.value.curStep % 8])

  // 速度调整
  state.value.speed = calcSpeed(state.value.speed)
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  timer.value = setTimeout(() => {
    state.value.curStep++
    startRun()
  }, state.value.speed)
}

const goRun = () => {
  if (!state.value.isRunning) {
    state.value.isRunning = true
    state.value.curStep = 0
    state.value.speed = initSpeed.value
    startRun()
  }
}

// 初始化数据
const initData = () => {
  state.value = { ..._state }
  state.value.speed = initSpeed.value
  console.log('fsfdsf')
}

defineExpose({
  initData,
  goRun
})
</script>

<template>
  <div class="award-warp">
    <slot name="award" :lottery-list="lotteryList" :state="state" />
  </div>
</template>

<style scoped lang="scss">
.award-warp {
  margin: 0 auto;
  @apply w-[600px] h-[600px] flex flex-wrap justify-between;
}
</style>
