export interface MySurveysVo {
  id: string;
  title: string;
  description: string;
  status: number;
  statusName: string;
  endDate: string;
}

export interface MySurveysListRes {
  count: number;
  list: MySurveysVo[];
}

export interface OptionInfos {
  optionText: string;
  score: number;
  sorted: number;
  id: string;
  isAnswer?: number;
  answerText?: string;
}

export interface SurveysDetailVo {
  questionType: number;
  questionTypeName?: string;
  questionText: string;
  id: string;
  required: number;
  hasScore: number;
  sorted: number;
  options: OptionInfos[];
  answer?: string | string[];
}

export interface SurveysDetailRes {
  title: string;
  questions: SurveysDetailVo[];
}

export interface SubmitAnswers {
  questionId?: string;
  optionIds?: string;
  answerText?: string;
}

export interface SubmitAnswersParam {
  id: string;
  answers?: SubmitAnswers;
}
